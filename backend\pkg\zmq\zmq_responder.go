package zmq

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/pebbe/zmq4"
)

// ZMQResponder ZeroMQ响应者实现
type ZMQResponder struct {
	config    *Config
	socket    *zmq4.Socket
	ctx       *Context
	logger    Logger
	handler   RequestHandler
	mu        sync.RWMutex
	connected bool
	running   bool
}

// NewResponder 创建新的响应者
func NewResponder(config *Config) (Responder, error) {
	if err := ValidateConfig(config); err != nil {
		return nil, fmt.Errorf("invalid config: %w", err)
	}

	ctx, err := GetGlobalContext()
	if err != nil {
		return nil, fmt.Errorf("get context failed: %w", err)
	}

	return &ZMQResponder{
		config: config,
		ctx:    ctx,
		logger: DefaultLogger,
	}, nil
}

// NewResponderWithLogger 创建带自定义日志的响应者
func NewResponderWithLogger(config *Config, logger Logger) (Responder, error) {
	resp, err := NewResponder(config)
	if err != nil {
		return nil, err
	}

	if logger != nil {
		resp.(*ZMQResponder).logger = logger
	}

	return resp, nil
}

// bind 绑定到指定端点
func (r *ZMQResponder) bind() error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if r.connected {
		return nil
	}

	// 创建REP套接字
	socket, err := zmq4.NewSocket(zmq4.REP)
	if err != nil {
		return NewZMQError("create_socket", err)
	}

	// 设置socket选项以提高错误处理能力
	if err := socket.SetLinger(0); err != nil {
		r.logger.Warn("Failed to set socket linger", "error", err)
	}

	// 设置套接字选项
	if err := socket.SetRcvtimeo(r.config.RecvTimeout); err != nil {
		socket.Close()
		return NewZMQError("set_rcvtimeo", err)
	}

	if err := socket.SetSndtimeo(r.config.SendTimeout); err != nil {
		socket.Close()
		return NewZMQError("set_sndtimeo", err)
	}

	if err := socket.SetSndhwm(r.config.SendHWM); err != nil {
		socket.Close()
		return NewZMQError("set_sndhwm", err)
	}

	if err := socket.SetRcvhwm(r.config.RecvHWM); err != nil {
		socket.Close()
		return NewZMQError("set_rcvhwm", err)
	}

	// 绑定到端点
	if err := socket.Bind(r.config.ResponderEndpoint); err != nil {
		socket.Close()
		return NewZMQError("bind", err)
	}

	r.socket = socket
	r.connected = true
	r.logger.Info("Responder bound", "endpoint", r.config.ResponderEndpoint)

	return nil
}

// SetHandler 设置请求处理函数
func (r *ZMQResponder) SetHandler(handler RequestHandler) error {
	if handler == nil {
		return fmt.Errorf("handler cannot be nil")
	}

	r.mu.Lock()
	defer r.mu.Unlock()

	r.handler = handler
	return nil
}

// Start 开始监听请求
func (r *ZMQResponder) Start(ctx context.Context) error {
	// 确保已设置处理函数
	r.mu.RLock()
	if r.handler == nil {
		r.mu.RUnlock()
		return fmt.Errorf("handler not set")
	}
	r.mu.RUnlock()

	// 绑定套接字
	if err := r.bind(); err != nil {
		return err
	}

	r.mu.Lock()
	if r.running {
		r.mu.Unlock()
		return fmt.Errorf("responder already running")
	}
	r.running = true
	r.mu.Unlock()

	r.logger.Info("Responder started")

	// 启动消息循环
	go r.messageLoop(ctx)

	// 等待上下文取消
	<-ctx.Done()

	r.mu.Lock()
	r.running = false
	r.mu.Unlock()

	r.logger.Info("Responder stopped")
	return nil
}

// messageLoop 消息处理循环
func (r *ZMQResponder) messageLoop(ctx context.Context) {
	for {
		select {
		case <-ctx.Done():
			return
		default:
			// 接收请求
			request, err := r.receiveRequest()
			if err != nil {
				// 检查是否是超时错误（没有消息可接收）
				if err.Error() == "resource temporarily unavailable" {
					// 超时，继续循环
					continue
				}
				// 其他错误已经在receiveRequest中记录了详细信息
				// 尝试捕获可能的无效数据
				r.tryReceiveRawData()
				// 这里只记录简要信息避免重复
				r.logger.Warn("⚠️ 跳过无效的ZMQ消息", "error_summary", err.Error())
				continue
			}

			// 记录接收到的原始数据（用于调试）
			r.logger.Debug("📨 ZMQ Responder接收到消息",
				"data_string", string(request),
				"data_hex", fmt.Sprintf("%x", request),
				"data_length", len(request))

			// 处理请求
			response, err := r.processRequest(request)
			if err != nil {
				r.logger.Error("Process request failed", "error", err)
				// 发送错误响应
				response = []byte(fmt.Sprintf("ERROR: %v", err))
			}

			// 发送响应
			if err := r.sendResponse(response); err != nil {
				r.logger.Error("Send response failed", "error", err)
			}
		}
	}
}

// receiveRequest 接收请求
func (r *ZMQResponder) receiveRequest() ([]byte, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	if !r.connected || r.socket == nil {
		return nil, fmt.Errorf("not connected")
	}

	// 尝试使用更底层的方法接收数据
	// 首先尝试接收消息帧
	frames, err := r.socket.RecvMessageBytes(0)
	if err != nil {
		// 记录底层ZMQ接收错误的详细信息
		r.logger.Error("🔥 ZMQ底层接收错误",
			"error", err,
			"error_type", fmt.Sprintf("%T", err),
			"endpoint", r.config.ResponderEndpoint)

		// 尝试用RecvBytes作为备用方法，可能能捕获到部分数据
		if data, err2 := r.socket.RecvBytes(zmq4.DONTWAIT); err2 == nil {
			r.logger.Error("🔍 捕获到的无效数据",
				"data_string", string(data),
				"data_hex", fmt.Sprintf("%x", data),
				"data_length", len(data))
		}

		return nil, err
	}

	// 如果成功接收到多帧消息，合并所有帧
	if len(frames) == 0 {
		return nil, fmt.Errorf("received empty message")
	}

	// 对于REP socket，通常只有一帧
	if len(frames) == 1 {
		return frames[0], nil
	}

	// 如果有多帧，记录并合并
	r.logger.Debug("📦 接收到多帧消息", "frame_count", len(frames))

	// 简单合并所有帧（实际应用中可能需要更复杂的处理）
	var combined []byte
	for i, frame := range frames {
		r.logger.Debug("📄 消息帧", "frame_index", i, "frame_length", len(frame))
		combined = append(combined, frame...)
	}

	return combined, nil
}

// tryReceiveRawData 尝试接收原始数据（用于调试无效消息）
func (r *ZMQResponder) tryReceiveRawData() {
	if !r.connected || r.socket == nil {
		return
	}

	// 尝试用DONTWAIT模式接收数据，避免阻塞
	if data, err := r.socket.RecvBytes(zmq4.DONTWAIT); err == nil && len(data) > 0 {
		r.logger.Error("🔍 捕获到可能的无效数据",
			"data_string", string(data),
			"data_hex", fmt.Sprintf("%x", data),
			"data_length", len(data),
			"first_bytes", fmt.Sprintf("%v", data[:min(len(data), 20)]))
	}
}

// min 辅助函数
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// processRequest 处理请求
func (r *ZMQResponder) processRequest(request []byte) ([]byte, error) {
	r.mu.RLock()
	handler := r.handler
	r.mu.RUnlock()

	if handler == nil {
		return nil, fmt.Errorf("no handler set")
	}

	// 使用defer捕获panic
	defer func() {
		if err := recover(); err != nil {
			r.logger.Error("Handler panic", "error", err)
		}
	}()

	// 记录处理时间
	start := time.Now()
	response, err := handler(request)
	duration := time.Since(start)

	if err != nil {
		r.logger.Warn("Handler error", "error", err, "duration", duration)
		return nil, err
	}

	r.logger.Debug("Request processed", "request_size", len(request), "response_size", len(response), "duration", duration)
	return response, nil
}

// sendResponse 发送响应
func (r *ZMQResponder) sendResponse(response []byte) error {
	r.mu.RLock()
	defer r.mu.RUnlock()

	if !r.connected || r.socket == nil {
		return fmt.Errorf("not connected")
	}

	_, err := r.socket.SendBytes(response, 0)
	return err
}

// Close 关闭响应者
func (r *ZMQResponder) Close() error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if !r.connected || r.socket == nil {
		return nil
	}

	if err := r.socket.Close(); err != nil {
		return NewZMQError("close_socket", err)
	}

	r.socket = nil
	r.connected = false
	r.running = false
	r.logger.Info("Responder closed")

	return nil
}

// IsConnected 检查连接状态
func (r *ZMQResponder) IsConnected() bool {
	r.mu.RLock()
	defer r.mu.RUnlock()
	return r.connected
}

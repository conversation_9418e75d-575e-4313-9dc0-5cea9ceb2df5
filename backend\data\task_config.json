{"system": {"robot_no": "AGV001", "description": "AGV导航控制系统"}, "parkingPoint": {"id": 1, "x": 0.0, "y": 0.0, "angle": 0.0}, "maxRetries": 3, "retryDelayBase": "5s", "plcAddresses": {"controlRequestAddress": 601, "controlConfirmAddress": 501, "taskStartAddress": 602, "readyAddress": 509, "directionAddress": 604, "distanceAddress": 602, "rollerAddress": 600, "completeAddress": 603, "verifyDirectionAddress": 505, "verifyRollerAddress": 501, "verifyDistanceAddress": 502, "verificationCompleteAddress": 605, "workStatusAddress": 500, "robotStartSignalAddress": 612, "machineCompleteAddress": 606, "returnToOriginAddress": 610, "originDirectionAddress": 611, "originCodeValueAddress": 604, "originArrivalAddress": 510, "controlHandoverAddress": 607, "turnAroundAddress": 608, "turnCompleteAddress": 511, "exitLaneAddress": 609, "laneExitCompleteAddress": 508}, "timeouts": {"navigationTimeout": "20m0s", "navigationCheckInterval": "2s", "plcReadyTimeout": "5m0s", "plcReadyCheckInterval": "500ms", "plcWorkTimeout": "5m0s", "plcWorkCheckInterval": "3s", "turnAroundTimeout": "10m0s", "turnAroundCheckInterval": "3s", "laneExitTimeout": "60s", "laneExitCheckInterval": "500ms", "originArrivalTimeout": "10m0s", "originArrivalCheckInterval": "3s"}, "logging": {"level": "INFO", "enableConsole": true, "enableFile": true, "enableStructured": false, "filePath": "logs/agv_nav.log", "maxSize": 10, "maxBackups": 5, "maxAge": 30}, "lane_configuration": {"special_lanes": [{"machine": "61R", "type": "central_aisle", "strategy": "single_only", "reason": "中央通道位置，只处理单侧"}, {"machine": "62L", "type": "skip", "strategy": "skip", "reason": "位置原因放弃处理"}], "lane_rules": {"default_pairing": "nL_with_n-1R", "entry_side": "L"}}, "safeTurnConfig": {"minSpindle": 50, "maxSpindle": 1100, "startAddress": 606, "endAddress": 608}, "cache": {"enable": true, "default_interval": 5, "default_machines": ["61", "60", "59", "58", "57"], "expiry_minutes": 30, "max_retries": 3, "retry_interval_seconds": 5, "data_storage_path": "backend/data/cache_data.json", "enable_file_storage": true, "auto_start_on_boot": false}, "mysql": {"host": "*************", "port": "3306", "username": "root", "password": "remote", "database": "schedule"}, "spindleDataSource": "mysql", "mysqlSpindleConfig": {"host": "localhost", "port": "3306", "username": "root", "password": "remote", "database": "schedule", "timeout": "30s"}, "allowedMachines": []}
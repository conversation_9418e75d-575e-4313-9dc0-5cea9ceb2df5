package auto_watch

import (
	"fmt"
	"strconv"

	"github.com/user/agv_nav/internal/database"
	"github.com/user/agv_nav/pkg/logger"
)

// AutoWatchSpindleData 自动看车锭号处理数据（复制自SpindleData）
type AutoWatchSpindleData struct {
	SpindleNo int     `json:"spindle_no"` // 锭号
	Distance  float32 `json:"distance"`   // 距离码值
	IsLeft    bool    `json:"is_left"`    // 是否左皮辊
	Direction int     `json:"direction"`  // 方向信息 (0或1)
}

// AutoWatchWorkExecutor 自动看车工作执行器（复制自task_service.go的executeWatchWork相关功能）
type AutoWatchWorkExecutor struct {
	spindleService    AutoWatchSpindleServiceInterface // 使用接口类型
	plcHandler        *AutoWatchPLCHandler
	navHandler        *AutoWatchNavigationHandler
	dbService         *database.NavigationDB
	taskManager       *AutoWatchTaskManager
	completionHandler *AutoWatchCompletionHandler
	logger            Logger
}

// NewAutoWatchWorkExecutor 创建自动看车工作执行器
func NewAutoWatchWorkExecutor(spindleService AutoWatchSpindleServiceInterface, plcHandler *AutoWatchPLCHandler, navHandler *AutoWatchNavigationHandler, logger Logger) *AutoWatchWorkExecutor {
	// 初始化数据库服务
	dbService, err := database.NewNavigationDB()
	if err != nil {
		logger.Warn("Initialize database connection failed", "error", err)
		dbService = nil
	}

	// 创建任务管理器
	taskManager := NewAutoWatchTaskManager(logger)

	executor := &AutoWatchWorkExecutor{
		spindleService: spindleService,
		plcHandler:     plcHandler,
		navHandler:     navHandler,
		dbService:      dbService,
		taskManager:    taskManager,
		logger:         logger,
	}

	// 注意：完成处理器将在SetService方法中创建

	return executor
}

// SetService 设置服务实例并创建完成处理器
func (e *AutoWatchWorkExecutor) SetService(service *AutoWatchService) {
	// 创建完成处理器
	e.completionHandler = NewAutoWatchCompletionHandler(
		e.taskManager,
		e.spindleService,
		e.plcHandler,
		e.navHandler,
		service,
		e.logger,
	)
}

// AutoExecuteWatchWork 自动看车执行看车工作（复制自executeWatchWork）
func (e *AutoWatchWorkExecutor) AutoExecuteWatchWork(machine string, callMachineComplete bool) error {
	e.logger.Info("Starting auto watch work execution", "machine", machine, "callMachineComplete", callMachineComplete)

	// 验证执行器是否已准备好执行任务
	if err := e.ValidateForExecution(); err != nil {
		return fmt.Errorf("auto watch work executor validation failed: %w", err)
	}

	// 更新任务状态：开始处理机器
	e.taskManager.UpdateMachineStart(machine)

	// 获取排序方向
	order, err := e.autoGetOrderByMachine(machine)
	if err != nil {
		e.logger.Warn("Failed to query machine sort order, using default ascending", "machine", machine, "error", err)
		order = 1
	}
	isAscending := order == 1
	e.logger.Info("Auto watch machine sort order", "machine", machine, "order", map[bool]string{true: "ascending", false: "descending"}[isAscending])

	var watermark int
	isWatermarkSet := false
	processedCount := 0

	for {
		// 步骤A：POST获取最新断头数据
		spindleList, err := e.autoGetSpindleList(machine)
		if err != nil {
			return fmt.Errorf("step A failed - get auto watch spindle list failed: %w", err)
		}
		e.logger.Info("Step A: Retrieved auto watch spindle list", "count", len(spindleList), "spindles", spindleList)

		if len(spindleList) == 0 {
			e.logger.Info("No spindle data found, auto watch work completed", "processedCount", processedCount)

			// 检测机器完成状态
			isComplete, err := e.completionHandler.AutoDetectMachineCompletion(machine)
			if err != nil {
				e.taskManager.AddError(fmt.Sprintf("Machine completion detection failed: %v", err))
				return fmt.Errorf("auto watch machine completion detection failed: %w", err)
			}

			if isComplete {
				// 处理机器完成
				err = e.completionHandler.AutoProcessMachineCompletion(machine, callMachineComplete)
				if err != nil {
					e.taskManager.AddError(fmt.Sprintf("Machine completion processing failed: %v", err))
					return fmt.Errorf("auto watch machine completion processing failed: %w", err)
				}
			}

			break
		}

		// 步骤B：根据数据库Order字段排序
		sortedSpindles := e.autoSortSpindlesByOrder(spindleList, machine)
		e.logger.Info("Step B: Auto watch spindles sorted", "sortedSpindles", sortedSpindles)

		// 步骤C：基于水位线过滤锭号
		var filteredSpindles []int
		for _, spindle := range sortedSpindles {
			if !isWatermarkSet {
				// 第一次处理，全部保留
				filteredSpindles = append(filteredSpindles, spindle)
			} else {
				// 根据排序方向和水位线过滤
				if isAscending {
					if spindle > watermark {
						filteredSpindles = append(filteredSpindles, spindle)
					}
				} else {
					if spindle < watermark {
						filteredSpindles = append(filteredSpindles, spindle)
					}
				}
			}
		}
		e.logger.Info("Step C: Auto watch spindles filtered by watermark", "filteredCount", len(filteredSpindles), "filteredSpindles", filteredSpindles)

		if len(filteredSpindles) == 0 {
			e.logger.Info("No new spindles to process, auto watch work completed")

			// 检测机器完成状态
			isComplete, err := e.completionHandler.AutoDetectMachineCompletion(machine)
			if err != nil {
				e.taskManager.AddError(fmt.Sprintf("Machine completion detection failed: %v", err))
				return fmt.Errorf("auto watch machine completion detection failed: %w", err)
			}

			if isComplete {
				// 处理机器完成
				err = e.completionHandler.AutoProcessMachineCompletion(machine, callMachineComplete)
				if err != nil {
					e.taskManager.AddError(fmt.Sprintf("Machine completion processing failed: %v", err))
					return fmt.Errorf("auto watch machine completion processing failed: %w", err)
				}
			}

			return nil // 直接返回，避免继续执行到函数结尾
		}

		// 步骤D：取第一个锭号
		currentSpindle := filteredSpindles[0]
		e.logger.Info("Step D: Selected auto watch spindle for processing", "spindle", currentSpindle)
		e.taskManager.UpdateSpindle(currentSpindle)
		e.taskManager.UpdateStep(fmt.Sprintf("Processing spindle %d", currentSpindle))

		// 步骤E：查询数据库获取距离码值（float）
		distance, err := e.autoGetSpindleCodeValue(machine, currentSpindle)
		if err != nil {
			return fmt.Errorf("step E failed - query auto watch spindle %d code value failed: %w", currentSpindle, err)
		}
		taskLogger := logger.GetModuleLogger("auto_watch")
		e.logger.Info("Step E: Auto watch spindle distance code value", "spindle", currentSpindle, "distance", distance)
		taskLogger.Info("Database read value", "spindle", currentSpindle, "distance", distance)

		// 步骤F：查询数据库获取Direction字段（用于PLC方向写入）
		direction, err := e.autoGetDirectionByMachine(machine)
		if err != nil {
			return fmt.Errorf("step F failed - query auto watch machine %s Direction field failed: %w", machine, err)
		}
		e.logger.Info("Step F: Auto watch machine Direction", "machine", machine, "direction", direction)

		// 步骤G：计算皮辊方向（根据锭号范围和奇偶性）
		isLeft, err := e.plcHandler.AutoWatchCalculateRollerDirection(currentSpindle)
		if err != nil {
			return fmt.Errorf("step G failed - calculate auto watch spindle %d roller direction failed: %w", currentSpindle, err)
		}
		e.logger.Info("Step G: Auto watch spindle roller direction", "spindle", currentSpindle, "direction", map[bool]string{true: "left", false: "right"}[isLeft])

		// 步骤H：PLC数据传输
		spindleData := AutoWatchSpindleData{
			SpindleNo: currentSpindle,
			Distance:  distance,
			IsLeft:    isLeft,
			Direction: direction,
		}
		err = e.plcHandler.AutoWatchSendSpindleDataToPLC(spindleData)
		if err != nil {
			return fmt.Errorf("step H failed - send auto watch spindle %d data to PLC failed: %w", currentSpindle, err)
		}
		e.logger.Info("Step H: Auto watch spindle data sent to PLC", "spindle", currentSpindle)

		// 步骤H2：验证PLC数据写入是否正确
		err = e.autoVerifyPLCData(spindleData)
		if err != nil {
			// 处理验证错误
			errHandled := e.completionHandler.AutoHandleProcessingError(machine, currentSpindle, err)
			if errHandled != nil {
				return fmt.Errorf("step H2 failed - verify auto watch spindle %d PLC data mismatch: %w", currentSpindle, err)
			}
		} else {
			e.logger.Info("Step H2: Auto watch spindle PLC data verification successful", "spindle", currentSpindle)
			taskLogger.Info("Data flow completed", "spindle", currentSpindle, "databaseValue", distance, "writeStatus", "success", "verifyStatus", "passed")
		}

		// 步骤H3：通知PLC数据验证完成
		err = e.autoNotifyVerificationComplete()
		if err != nil {
			// 处理通知错误
			errHandled := e.completionHandler.AutoHandleProcessingError(machine, currentSpindle, err)
			if errHandled != nil {
				return fmt.Errorf("step H3 failed - send auto watch spindle %d verification complete signal failed: %w", currentSpindle, err)
			}
		} else {
			e.logger.Info("Step H3: Auto watch spindle verification complete signal sent to PLC", "spindle", currentSpindle)
		}

		// 步骤I：等待PLC完成工作，使用完成检测器
		err = e.completionHandler.AutoDetectSpindleCompletion(currentSpindle)
		if err != nil {
			// 处理完成检测错误
			errHandled := e.completionHandler.AutoHandleProcessingError(machine, currentSpindle, err)
			if errHandled != nil {
				return fmt.Errorf("step I failed - wait for auto watch spindle %d PLC work complete failed: %w", currentSpindle, err)
			}
		} else {
			e.logger.Info("Step I: Auto watch spindle PLC work completed", "spindle", currentSpindle)
		}

		// 步骤J：更新水位线
		watermark = currentSpindle
		isWatermarkSet = true
		processedCount++
		e.logger.Info("Step J: Auto watch spindle processed, watermark updated", "spindle", currentSpindle, "watermark", watermark, "processedCount", processedCount)

		// 步骤K：继续循环
	}

	e.logger.Info("Auto watch machine work completed", "machine", machine, "totalProcessed", processedCount)

	// 最终检测机器完成状态
	isComplete, err := e.completionHandler.AutoDetectMachineCompletion(machine)
	if err != nil {
		e.taskManager.AddError(fmt.Sprintf("Final machine completion detection failed: %v", err))
		return fmt.Errorf("auto watch final machine completion detection failed: %w", err)
	}

	if isComplete {
		// 处理机器完成
		err = e.completionHandler.AutoProcessMachineCompletion(machine, callMachineComplete)
		if err != nil {
			e.taskManager.AddError(fmt.Sprintf("Final machine completion processing failed: %v", err))
			return fmt.Errorf("auto watch final machine completion processing failed: %w", err)
		}
	}

	return nil
}

// ExecuteWatchWork 兼容接口方法
func (e *AutoWatchWorkExecutor) ExecuteWatchWork(machine string, callMachineComplete bool) error {
	return e.AutoExecuteWatchWork(machine, callMachineComplete)
}

// 核心业务逻辑辅助方法

// autoGetSpindleList 自动看车通过POST请求获取细纱机的锭号列表（复制自getSpindleList）
func (e *AutoWatchWorkExecutor) autoGetSpindleList(machine string) ([]int, error) {
	return e.spindleService.GetAutoWatchSpindleList(machine)
}

// autoSortSpindlesByOrder 自动看车根据Order字段排序锭号（复制自sortSpindlesByOrder）
func (e *AutoWatchWorkExecutor) autoSortSpindlesByOrder(spindleList []int, machine string) []int {
	return e.spindleService.SortAutoWatchSpindlesByOrder(spindleList, machine)
}

// autoGetSpindleCodeValue 自动看车从数据库查询单个锭号对应的码值（复制自getSpindleCodeValue）
func (e *AutoWatchWorkExecutor) autoGetSpindleCodeValue(machine string, spindleNo int) (float32, error) {
	// 构造查询key：细纱机号+锭号
	spindleKey := machine + strconv.Itoa(spindleNo)
	e.logger.Info("Query auto watch spindle code value", "key", spindleKey)

	// 检查数据库服务是否可用
	if e.dbService == nil {
		return 0, fmt.Errorf("database service not initialized")
	}

	// 通过数据库服务查询SpindleDistance表
	value, err := e.dbService.GetSpindleDistance(spindleKey)
	if err != nil {
		return 0, err
	}

	e.logger.Info("Auto watch spindle code value retrieved", "spindleKey", spindleKey, "value", value)
	return value, nil
}

// autoGetDirectionByMachine 自动看车从数据库查询细纱机的Direction字段（复制自getDirectionByMachine）
func (e *AutoWatchWorkExecutor) autoGetDirectionByMachine(machine string) (int, error) {
	e.logger.Info("Query auto watch machine Direction field", "machine", machine)

	// 检查数据库服务是否可用
	if e.dbService == nil {
		return 0, fmt.Errorf("database service not initialized")
	}

	direction, err := e.dbService.GetDirectionByMachine(machine)
	if err != nil {
		return 0, err
	}

	e.logger.Info("Auto watch machine Direction retrieved", "machine", machine, "direction", direction)
	return direction, nil
}

// autoGetOrderByMachine 自动看车从数据库查询细纱机的Order字段（复制自类似逻辑）
func (e *AutoWatchWorkExecutor) autoGetOrderByMachine(machine string) (int, error) {
	e.logger.Info("Query auto watch machine Order field", "machine", machine)

	// 检查数据库服务是否可用
	if e.dbService == nil {
		return 1, fmt.Errorf("database service not initialized")
	}

	order, err := e.dbService.GetOrderByMachine(machine)
	if err != nil {
		return 1, err
	}

	e.logger.Info("Auto watch machine Order retrieved", "machine", machine, "order", order)
	return order, nil
}

// PLC数据处理辅助方法

// autoVerifyPLCData 自动看车验证PLC数据写入是否正确（复制自VerifyPLCData）
func (e *AutoWatchWorkExecutor) autoVerifyPLCData(data AutoWatchSpindleData) error {
	e.logger.Info("Auto watch verify PLC data", "spindle", data.SpindleNo)

	// 调用PLC handler的验证方法
	return e.plcHandler.AutoWatchVerifyPLCData(data)
}

// autoNotifyVerificationComplete 自动看车通知PLC数据验证完成（复制自NotifyVerificationComplete）
func (e *AutoWatchWorkExecutor) autoNotifyVerificationComplete() error {
	e.logger.Info("Auto watch notify verification complete")

	// 调用PLC handler的通知方法
	return e.plcHandler.AutoWatchNotifyVerificationComplete()
}



// ValidateAutoWatchWorkExecutor 验证自动看车工作执行器配置
func (e *AutoWatchWorkExecutor) ValidateAutoWatchWorkExecutor() error {
	if e.spindleService == nil {
		return fmt.Errorf("spindle service is nil")
	}

	if e.plcHandler == nil {
		return fmt.Errorf("PLC handler is nil")
	}

	if e.navHandler == nil {
		return fmt.Errorf("navigation handler is nil")
	}

	if e.taskManager == nil {
		return fmt.Errorf("task manager is nil")
	}

	e.logger.Info("Auto watch work executor validation passed",
		"spindleServiceAvailable", e.spindleService != nil,
		"plcHandlerAvailable", e.plcHandler != nil,
		"navHandlerAvailable", e.navHandler != nil,
		"taskManagerAvailable", e.taskManager != nil,
		"completionHandlerAvailable", e.completionHandler != nil,
		"dbAvailable", e.dbService != nil)

	return nil
}

// ValidateForExecution 验证执行器是否已准备好执行任务（包括completion handler）
func (e *AutoWatchWorkExecutor) ValidateForExecution() error {
	// 首先验证基础组件
	if err := e.ValidateAutoWatchWorkExecutor(); err != nil {
		return err
	}

	// 验证completion handler（执行时必需）
	if e.completionHandler == nil {
		return fmt.Errorf("completion handler is nil - SetService must be called before execution")
	}

	e.logger.Info("Auto watch work executor ready for execution",
		"allComponentsAvailable", true)

	return nil
}

// GetAutoWatchWorkExecutorStatus 获取自动看车工作执行器状态
func (e *AutoWatchWorkExecutor) GetAutoWatchWorkExecutorStatus() map[string]interface{} {
	status := map[string]interface{}{
		"spindleServiceAvailable":    e.spindleService != nil,
		"plcHandlerAvailable":        e.plcHandler != nil,
		"navHandlerAvailable":        e.navHandler != nil,
		"taskManagerAvailable":       e.taskManager != nil,
		"completionHandlerAvailable": e.completionHandler != nil,
		"dbAvailable":                e.dbService != nil,
	}

	// 如果任务管理器可用，添加任务状态
	if e.taskManager != nil {
		taskStatus := e.taskManager.GetTaskStatus()
		status["taskStatus"] = taskStatus
	}

	// 如果完成处理器可用，添加完成状态
	if e.completionHandler != nil {
		completionStatus := e.completionHandler.GetAutoWatchCompletionStatus()
		status["completionStatus"] = completionStatus
	}

	return status
}

// GetTaskManager 获取任务管理器（供外部使用）
func (e *AutoWatchWorkExecutor) GetTaskManager() *AutoWatchTaskManager {
	return e.taskManager
}

// GetCompletionHandler 获取完成处理器（供外部使用）
func (e *AutoWatchWorkExecutor) GetCompletionHandler() *AutoWatchCompletionHandler {
	return e.completionHandler
}

// IsReadyForExecution 检查执行器是否已准备好执行任务
func (e *AutoWatchWorkExecutor) IsReadyForExecution() bool {
	return e.ValidateForExecution() == nil
}

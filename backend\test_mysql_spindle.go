package main

import (
	"fmt"
	"log"

	"github.com/user/agv_nav/internal/auto_watch"
	"github.com/user/agv_nav/internal/task"
)

// 简单的日志实现
type testLogger struct{}

func (l *testLogger) Debug(msg string, fields ...interface{}) {
	fmt.Printf("[DEBUG] %s %v\n", msg, fields)
}

func (l *testLogger) Info(msg string, fields ...interface{}) {
	fmt.Printf("[INFO] %s %v\n", msg, fields)
}

func (l *testLogger) Warn(msg string, fields ...interface{}) {
	fmt.Printf("[WARN] %s %v\n", msg, fields)
}

func (l *testLogger) Error(msg string, fields ...interface{}) {
	fmt.Printf("[ERROR] %s %v\n", msg, fields)
}

func main() {
	fmt.Println("=== MySQL断头数据源测试 ===")
	
	// 初始化配置
	err := task.InitConfig()
	if err != nil {
		log.Fatalf("初始化配置失败: %v", err)
	}
	
	// 获取配置
	config := task.GetConfig()
	fmt.Printf("当前数据源配置: %s\n", config.SpindleDataSource)
	fmt.Printf("MySQL配置: %+v\n", config.MySQLSpindleConfig)
	
	// 创建锭号服务
	logger := &testLogger{}
	service, err := auto_watch.CreateAutoWatchSpindleService(logger)
	if err != nil {
		log.Fatalf("创建锭号服务失败: %v", err)
	}
	
	fmt.Printf("锭号服务创建成功，类型: %T\n", service)
	
	// 测试不同数据源的创建
	fmt.Println("\n--- 测试MES数据源 ---")
	config.SpindleDataSource = "mes"
	mesService, err := auto_watch.CreateAutoWatchSpindleService(logger)
	if err != nil {
		fmt.Printf("MES服务创建失败: %v\n", err)
	} else {
		fmt.Printf("MES服务创建成功，类型: %T\n", mesService)
	}
	
	fmt.Println("\n--- 测试MySQL数据源 ---")
	config.SpindleDataSource = "mysql"
	mysqlService, err := auto_watch.CreateAutoWatchSpindleService(logger)
	if err != nil {
		fmt.Printf("MySQL服务创建失败: %v\n", err)
	} else {
		fmt.Printf("MySQL服务创建成功，类型: %T\n", mysqlService)
		
		// 测试MySQL查询（这会失败，因为没有真实的数据库连接，但可以验证逻辑）
		fmt.Println("\n--- 测试MySQL查询逻辑 ---")
		spindles, err := mysqlService.GetAutoWatchSpindleList("61R")
		if err != nil {
			fmt.Printf("预期的MySQL查询失败: %v\n", err)
		} else {
			fmt.Printf("意外的成功结果: %v\n", spindles)
		}
	}
	
	fmt.Println("\n=== 测试完成 ===")
}

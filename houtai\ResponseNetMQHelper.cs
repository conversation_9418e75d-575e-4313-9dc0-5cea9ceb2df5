﻿using BackendSchedulingSystem.Helpers.Handlers;
using BackendSchedulingSystem.Models;
using NetMQ;
using NetMQ.Sockets;

namespace BackendSchedulingSystem.Helpers.NetMQ
{
    /// <summary>
    /// NetMQ 工具类 - 接收响应并发送消息
    /// 基于NetMQ的ResponseSocket实现请求-响应模式的消息处理
    /// </summary>
    public static class ResponseNetMQHelper
    {
        private static string _logName = "NetMQ 工具类 - 接收响应并发送消息";

        /// <summary>
        /// 响应套接字，用于与客户端通信
        /// </summary>
        private static ResponseSocket? _socket;

        /// <summary>
        /// 取消令牌源，用于优雅地停止消息处理循环
        /// </summary>
        private static CancellationTokenSource? _cancellationTokenSource;

        /// <summary>
        /// 初始化锁，确保线程安全的单例初始化
        /// </summary>
        private static readonly SemaphoreSlim _initializationLock = new(1, 1);

        private static readonly NetMQHandler _netMQHandler = new();

        /// <summary>
        /// 服务运行状态标识
        /// </summary>
        private static bool _isRunning = false;

        /// <summary>
        /// 消息处理线程，在后台持续运行
        /// </summary>
        private static Thread? _processingThread { get; set; }

        /// <summary>
        /// 创建并启动NetMQ服务（在新线程中运行）
        /// </summary>
        public static void StartNetMQ()
        {
            // 使用信号量确保初始化操作的线程安全
            _initializationLock.Wait();

            string logName = _logName + Environment.NewLine + "创建并启动NetMQ服务（在新线程中运行）";

            try
            {
                // 防止重复启动
                if (_isRunning)
                {
                    NLogHelper.Info(logName, "NetMQ服务已在运行中，无需重复启动");
                    return;
                }

                // 从配置中获取服务地址并绑定
                NLogHelper.Info(logName, $"服务器【{AppSettings.System.Address}】已启动，等待客户端连接...");

                // 创建并配置响应套接字
                _socket = new ResponseSocket();
                _socket.Bind(AppSettings.System.Address);

                // 初始化取消令牌源
                _cancellationTokenSource = new CancellationTokenSource();
                _isRunning = true;

                // 创建并启动处理线程（设置为后台线程，主程序退出时自动终止）
                _processingThread = new Thread(ProcessingLoop)
                {
                    IsBackground = true
                };
                _processingThread.Start();
            }
            catch (Exception ex)
            {
                NLogHelper.Fatal(logName, "创建并启动NetMQ服务时：发生未知异常", ex);
            }
            finally
            {
                _initializationLock.Release();
            }
        }

        /// <summary>
        /// 消息处理主循环（在单独线程中运行）
        /// 负责接收客户端消息、处理消息并返回响应
        /// </summary>
        private static void ProcessingLoop()
        {
            string logName = _logName + Environment.NewLine + "消息处理主循环（在单独线程中运行） 负责接收客户端消息、处理消息并返回响应";

            try
            {
                if (_cancellationTokenSource == null) _cancellationTokenSource = new CancellationTokenSource();

                if (_socket == null)
                {
                    _socket = new ResponseSocket();
                    _socket.Bind(AppSettings.System.Address);
                }

                // 主处理循环，持续接收和处理消息直到服务停止
                while (!_cancellationTokenSource.Token.IsCancellationRequested)
                {
                    try
                    {
                        // 阻塞接收客户端消息（线程会在此处等待，直到收到消息）
                        string request = _socket.ReceiveFrameString();
                        NLogHelper.NetMQResponseReceiveRecord(request);

                        // 调用服务层处理消息并获取响应
                        // 序列化响应消息并发送给客户端
                        NetMQMsgResponse responseMsg = _netMQHandler.ProcessPassivelyReceivedMessage(request);
                        NLogHelper.NetMQResponseSendRecord(responseMsg.Message);

                        _socket.SendFrame(responseMsg.Message);

                        Task.Run(() => _netMQHandler.ReplyAndThenSendMessage(responseMsg));
                    }
                    catch (OperationCanceledException ex)
                    {
                        // 服务取消请求，忽略异常并退出循环
                        NLogHelper.Fatal(logName, "服务取消请求，忽略异常并退出循环", ex);
                    }
                    catch (Exception ex)
                    {
                        // 处理消息过程中发生异常，记录错误并返回错误响应
                        NLogHelper.Fatal(logName, "处理客户端请求时发生异常", ex);

                        // 尝试发送错误响应给客户端
                        try
                        {
                            string errorResponse = NetMQMsgHelper.ReplyFailed("处理请求时发生异常");
                            NLogHelper.NetMQResponseSendRecord(errorResponse);
                            _socket.SendFrame(errorResponse);
                        }
                        catch (Exception innerEx)
                        {
                            // 发送错误响应失败，记录内部错误
                            NLogHelper.Fatal(logName, "发送错误响应失败", innerEx);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // 处理循环发生致命错误，记录错误
                NLogHelper.Error(logName, "NetMQ处理循环发生致命错误", ex);
            }
            finally
            {
                // 无论如何，确保清理资源并更新运行状态
                _isRunning = false;
                CleanupResources();
            }
        }

        /// <summary>
        /// 停止NetMQ服务
        /// 优雅地关闭服务，包括取消处理任务、关闭套接字和等待线程结束
        /// </summary>
        public static void StopNetMQ()
        {
            _initializationLock.Wait();

            string logName = _logName + Environment.NewLine + "停止NetMQ服务 优雅地关闭服务，包括取消处理任务、关闭套接字和等待线程结束";

            try
            {
                // 检查服务是否正在运行
                if (!_isRunning)
                {
                    NLogHelper.Info(logName, "NetMQ服务未在运行中");
                    return;
                }

                NLogHelper.Info(logName, "正在停止NetMQ服务...");

                // 取消处理任务
                _cancellationTokenSource?.Cancel();

                // 关闭套接字以解除可能的阻塞状态（如果线程正在等待消息）
                try
                {
                    _socket?.Close();
                }
                catch (Exception ex)
                {
                    NLogHelper.Error(logName, "关闭套接字时发生异常", ex);
                }

                // 等待线程完成
                if (_processingThread != null && _processingThread.IsAlive)
                {
                    try
                    {
                        _processingThread.Join(2000); // 等待最多2秒
                    }
                    catch (Exception ex)
                    {
                        NLogHelper.Error(logName, "等待NetMQ线程结束时发生异常", ex);
                    }
                }

                // 清理资源
                CleanupResources();

                NLogHelper.Info(logName, "NetMQ服务已停止");
            }
            finally
            {
                _initializationLock.Release();
            }
        }

        /// <summary>
        /// 清理NetMQ相关资源
        /// 安全地释放套接字和取消令牌源
        /// </summary>
        private static void CleanupResources()
        {
            string logName = _logName + Environment.NewLine + "清理NetMQ相关资源 安全地释放套接字和取消令牌源";

            try
            {
                // 关闭并释放套接字资源
                _socket?.Close();
                _socket?.Dispose();
                _socket = null;

                // 释放取消令牌源
                _cancellationTokenSource?.Dispose();
                _cancellationTokenSource = null;
            }
            catch (Exception ex)
            {
                // 记录资源清理过程中的异常
                NLogHelper.Error(logName, "清理NetMQ资源时发生异常", ex);
            }
        }
    }
}
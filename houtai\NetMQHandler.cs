﻿using BackendSchedulingSystem.Dictionaries.Enums;
using BackendSchedulingSystem.Models;
using BackendSchedulingSystem.Models.Handlers;
using BackendSchedulingSystem.Models.Inputs;
using BackendSchedulingSystem.Services.Handlers;
using System.Diagnostics;
using System.Text.Json;

namespace BackendSchedulingSystem.Helpers.Handlers
{
    /// <summary>
    /// NetMQ
    /// </summary>
    public class NetMQHandler
    {
        private RobotHandler _robotHandler { get; set; }
        private RobotServiceHandler _robotServiceHandler { get; set; }
        private NetMQMsgHandler _netMQMsgHandler { get; set; }
        
        public NetMQHandler()
        {
            _robotHandler = new RobotHandler();
            _robotServiceHandler = new RobotServiceHandler();
            _netMQMsgHandler = new NetMQMsgHandler();
        }

        /// <summary>
        /// 处理主动接收消息（发送消息并接收响应）
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public bool ProcessActivelyReceivedMessage(string? request)
        {
            try
            {
                if (string.IsNullOrEmpty(request))
                {
                    NLogHelper.Error("处理主动接收消息（发送消息并接收响应）", $"消息为空：{request}");
                    return false;
                }

                NetMQMsg? requestMessage = JsonSerializer.Deserialize<NetMQMsg>(request);
                if (requestMessage == null)
                {
                    NLogHelper.Error("处理主动接收消息（发送消息并接收响应）", $"未知模块：{request}");
                    return false;
                }

                if (!requestMessage.code)
                {
                    if (!string.IsNullOrEmpty(requestMessage.message))
                    {
                        NLogHelper.Error("处理主动接收消息（发送消息并接收响应）", requestMessage.message);
                    }
                    else
                    {
                        NLogHelper.Error("处理主动接收消息（发送消息并接收响应）", "失败");
                    }
                    return false;
                }

                InstructionEnum instruction = (InstructionEnum)requestMessage.instruction;

                switch (instruction)
                {
                    case InstructionEnum.Error:
                        if (!string.IsNullOrEmpty(requestMessage.message))
                        {
                            NLogHelper.Error("处理主动接收消息（接收响应并发送消息）", requestMessage.message);
                        }
                        else
                        {
                            NLogHelper.Error("处理主动接收消息（发送消息并接收响应）", "异常");
                        }

                        break;
                    case InstructionEnum.Reply:
                        break;
                    default:
                        break;
                }

                return true;
            }
            catch (JsonException jsonEx)
            {
                NLogHelper.Fatal("处理主动接收消息（发送消息并接收响应）", $"发生JSON解析异常；原始内容: {request}", jsonEx);
                return false;
            }
            catch (Exception ex)
            {
                NLogHelper.Fatal("处理主动接收消息（发送消息并接收响应）", $"发生未知异常；原始内容: {request}", ex);
                return false;
            }
        }

        /// <summary>
        /// 处理被动接收消息（接收响应并发送消息）
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public NetMQMsgResponse ProcessPassivelyReceivedMessage(string? request)
        {
            NetMQMsgResponse responseMsg = new()
            {
                State = false
            };

            try
            {
                if (string.IsNullOrEmpty(request))
                {
                    NLogHelper.Error("处理被动接收消息（接收响应并发送消息）", $"消息为空：{request}");

                    responseMsg.Message = NetMQMsgHelper.ReplyFailed("消息为空");
                    return responseMsg;
                }

                // 解析消息
                NetMQMsg? requestMessage = JsonSerializer.Deserialize<NetMQMsg>(request);
                if (requestMessage == null)
                {
                    NLogHelper.Error("处理被动接收消息（接收响应并发送消息）", $"未知模块：{request}");

                    responseMsg.Message = NetMQMsgHelper.ReplyFailed("未知模块");
                    return responseMsg;
                }

                if (!requestMessage.code)
                {
                    if (!string.IsNullOrEmpty(requestMessage.message))
                    {
                        NLogHelper.Error("处理被动接收消息（接收响应并发送消息）", requestMessage.message);
                    }
                    else
                    {
                        NLogHelper.Error("处理被动接收消息（接收响应并发送消息）", "失败");
                    }

                    responseMsg.Message = NetMQMsgHelper.ReplySuccess();
                    return responseMsg;
                }

                if (requestMessage.content == null)
                {
                    NLogHelper.Error("处理被动接收消息（接收响应并发送消息）", $"内容为空：{request}");

                    responseMsg.Message = NetMQMsgHelper.ReplyFailed("内容为空");
                    return responseMsg;
                }

                responseMsg.Message = NetMQMsgHelper.ReplyFailed($"指令未做处理，指令：{requestMessage.instruction}");

                InstructionEnum instruction = (InstructionEnum)requestMessage.instruction;

                responseMsg.IsUpdate = false;
                responseMsg.Instruction = instruction;
                // 解析消息内容
                string contentJSON = JsonSerializer.Serialize(requestMessage.content);

                switch (instruction)
                {
                    case InstructionEnum.Error:
                        if (!string.IsNullOrEmpty(requestMessage.message))
                        {
                            NLogHelper.Error("处理被动接收消息（接收响应并发送消息）", requestMessage.message);
                        }
                        else
                        {
                            NLogHelper.Error("处理被动接收消息（接收响应并发送消息）", "异常");
                        }

                        responseMsg.IsUpdate = true;
                        break;
                    case InstructionEnum.Heartbeat:
                        RobotHeartbeatInput? heartbeatInput = JsonSerializer.Deserialize<RobotHeartbeatInput>(contentJSON);

                        if (heartbeatInput == null || string.IsNullOrEmpty(heartbeatInput.robotNo))
                        {
                            string message = $"错误：无法解析机器人状态消息或缺少RobotNo，原始内容: {contentJSON}";
                            NLogHelper.Warn("处理被动接收消息（接收响应并发送消息）", $"错误：无法解析机器人状态消息或缺少RobotNo，原始内容: {contentJSON}");

                            responseMsg.Message = NetMQMsgHelper.ReplyFailed("错误：无法解析机器人状态消息或缺少RobotNo");
                            return responseMsg;
                        }

                        responseMsg.IsUpdate = _robotHandler.UpdateHeartbeat(heartbeatInput);

                        if (responseMsg.IsUpdate)
                        {
                            responseMsg.RobotNo = heartbeatInput.robotNo;
                        }

                        responseMsg.Message = responseMsg.IsUpdate ? NetMQMsgHelper.ReplySuccess() : NetMQMsgHelper.ReplyFailed();
                        break;
                    case InstructionEnum.Reply:
                        break;
                    case InstructionEnum.StatusReport:
                    case InstructionEnum.TaskStart:
                    case InstructionEnum.TaskPause:
                    case InstructionEnum.TaskComplete:
                    case InstructionEnum.TaskForceStop:
                    case InstructionEnum.TaskCancel:
                        RobotStateInput? stateInput = JsonSerializer.Deserialize<RobotStateInput>(contentJSON);

                        if (stateInput == null || string.IsNullOrEmpty(stateInput.robotNo))
                        {
                            NLogHelper.Warn("处理被动接收消息（接收响应并发送消息）", $"错误：无法解析机器人状态消息或缺少RobotNo，原始内容: {contentJSON}");

                            responseMsg.Message = NetMQMsgHelper.ReplyFailed("错误：无法解析机器人状态消息或缺少RobotNo");
                            return responseMsg;
                        }

                        if (instruction == InstructionEnum.StatusReport)
                        {
                            responseMsg.IsUpdate = _robotHandler.UpdateRobotData(stateInput);
                        }
                        else if (instruction == InstructionEnum.TaskStart)
                        {
                            responseMsg.IsUpdate = _robotHandler.UpdateRobotTaskState(stateInput, RobotTaskStatusEnum.InProgress);
                        }
                        else if (instruction == InstructionEnum.TaskPause)
                        {
                            responseMsg.IsUpdate = _robotHandler.UpdateRobotTaskState(stateInput, RobotTaskStatusEnum.Paused);
                        }
                        else if (instruction == InstructionEnum.TaskComplete)
                        {
                            responseMsg.IsUpdate = _robotHandler.UpdateRobotTaskState(stateInput, RobotTaskStatusEnum.Completed);

                            if (responseMsg.IsUpdate)
                            {
                                responseMsg.RobotNo = stateInput.robotNo;
                            }
                        }
                        else if (instruction == InstructionEnum.TaskForceStop)
                        {
                            responseMsg.IsUpdate = _robotHandler.UpdateRobotTaskState(stateInput, RobotTaskStatusEnum.ForceStopped);

                            if (responseMsg.IsUpdate)
                            {
                                responseMsg.RobotNo = stateInput.robotNo;
                            }
                        }
                        else if (instruction == InstructionEnum.TaskCancel)
                        {
                            responseMsg.IsUpdate = _robotHandler.UpdateRobotTaskState(stateInput, RobotTaskStatusEnum.Cancelled);

                            if (responseMsg.IsUpdate)
                            {
                                responseMsg.RobotNo = stateInput.robotNo;
                            }
                        }

                        responseMsg.Message = responseMsg.IsUpdate ? NetMQMsgHelper.ReplySuccess() : NetMQMsgHelper.ReplyFailed();
                        break;
                    case InstructionEnum.TaskAssign:
                        break;
                    case InstructionEnum.NoTask:
                        break;
                    case InstructionEnum.WaitingForTask:
                        break;
                    case InstructionEnum.ContinueTask:
                        break;
                    case InstructionEnum.RobotStop:
                        break;
                    case InstructionEnum.RobotMove:
                        break;
                    default:
                        break;
                }

                return responseMsg;
            }
            catch (JsonException jsonEx)
            {
                NLogHelper.Fatal("处理被动接收消息（接收响应并发送消息）", $"发生JSON解析异常；原始内容: {request}", jsonEx);
                responseMsg.Message = NetMQMsgHelper.ReplyFailed("发生JSON解析异常");
                return responseMsg;
            }
            catch (Exception ex)
            {
                NLogHelper.Fatal("处理被动接收消息（接收响应并发送消息）", $"发生未知异常；原始内容: {request}", ex);
                responseMsg.Message = NetMQMsgHelper.ReplyFailed("发生未知异常");
                return responseMsg;
            }
        }

        /// <summary>
        /// 回复后发送消息（接收响应并发送消息）
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<bool> ReplyAndThenSendMessage(NetMQMsgResponse responseMsg)
        {
            try
            {
                if (!responseMsg.IsUpdate)
                {
                    return true;
                }

                bool isPush = false;

                if (responseMsg.Instruction == InstructionEnum.Heartbeat)
                {
                    if (!string.IsNullOrEmpty(responseMsg.RobotNo))
                    {
                        _netMQMsgHandler.SendWithDelayRobotHeartbeat(responseMsg.RobotNo);

                        isPush = true;
                    }
                }
                else if (responseMsg.Instruction == InstructionEnum.TaskComplete || responseMsg.Instruction == InstructionEnum.TaskForceStop || responseMsg.Instruction == InstructionEnum.TaskCancel)
                {
                    if (!string.IsNullOrEmpty(responseMsg.RobotNo))
                    {
                        isPush = await _robotServiceHandler.TaskCompletedAndAssigned(responseMsg.RobotNo);
                    }
                }

                return isPush;
            }
            catch (Exception ex)
            {
                NLogHelper.Fatal("回复后发送消息（接收响应并发送消息）", "发生未知异常", ex);
                return false;
            }
        }
    }
}

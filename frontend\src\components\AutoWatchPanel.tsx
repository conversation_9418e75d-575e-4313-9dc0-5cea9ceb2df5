import React, { useState, useEffect } from 'react'
import { startAutoWatch, stopAutoWatch, getAutoWatchStatus, oneClickOnline } from '../services/api'

// 心跳测试相关接口
interface HeartbeatTestResult {
  success: boolean
  responseTime: number
  error?: string
  timestamp: string
}

interface HeartbeatTestStatus {
  isRunning: boolean
  successCount: number
  failCount: number
  lastResult?: HeartbeatTestResult
  startTime?: string
  nextTestTime?: string
}

// 任务测试相关接口
interface TaskTestResult {
  taskId: string
  instruction: number
  robotNo: string
  laneNos: string[]
  remark: string
  success: boolean
  responseTime: number
  message: string
  timestamp: string
}

interface TaskTestStatus {
  taskTestEnabled: boolean
  totalTasks: number
  successCount: number
  failCount: number
  recentTasks: TaskTestResult[]
}

// 任务通知结果接口
interface TaskNotificationResult {
  taskId: string
  instruction: number // 110=任务开始, 130=任务完成
  robotNo: string
  remark: string
  success: boolean
  responseTime: number
  message: string
  timestamp: string
}

// 任务生命周期状态接口
interface TaskLifecycleStatus {
  currentTaskId: string
  taskState: string // "none", "received", "started", "completed"
  taskStartTime?: string
  taskCompleteTime?: string
  startNotifications: TaskNotificationResult[]
  completeNotifications: TaskNotificationResult[]
}

// 组合的测试状态接口
interface CombinedTestStatus {
  heartbeat: HeartbeatTestStatus
  task: TaskTestStatus
  taskLifecycle: TaskLifecycleStatus
}

interface AutoWatchStatus {
  isRunning: boolean
  isConnected: boolean
  robotNo: string
  currentTask: {
    taskId: string
    machineNo: string
    taskType: string
    status: string
    progress: number
  } | null
  statistics: {
    totalTasks: number
    completedTasks: number
    failedTasks: number
    totalRunTime: number
  }
  lastHeartbeat: string | null
  connectionStatus: 'disconnected' | 'connecting' | 'connected' | 'error'
}

const AutoWatchPanel: React.FC = () => {
  const [status, setStatus] = useState<AutoWatchStatus>({
    isRunning: false,
    isConnected: false,
    robotNo: 'AGV001',
    currentTask: null,
    statistics: {
      totalTasks: 0,
      completedTasks: 0,
      failedTasks: 0,
      totalRunTime: 0
    },
    lastHeartbeat: null,
    connectionStatus: 'disconnected'
  })
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [logs, setLogs] = useState<string[]>([])

  // 心跳测试状态
  const [heartbeatTest, setHeartbeatTest] = useState<HeartbeatTestStatus>({
    isRunning: false,
    successCount: 0,
    failCount: 0
  })

  // 任务测试状态
  const [taskTest, setTaskTest] = useState<TaskTestStatus>({
    taskTestEnabled: false,
    totalTasks: 0,
    successCount: 0,
    failCount: 0,
    recentTasks: []
  })

  // 任务生命周期状态
  const [taskLifecycle, setTaskLifecycle] = useState<TaskLifecycleStatus>({
    currentTaskId: '',
    taskState: 'none',
    startNotifications: [],
    completeNotifications: []
  })

  const [heartbeatLoading, setHeartbeatLoading] = useState(false)

  // 心跳测试API调用
  const startHeartbeatTest = async () => {
    try {
      setHeartbeatLoading(true)
      const response = await fetch('/api/zmq/heartbeat-test/start', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      })
      const data = await response.json()

      if (data.success) {
        addLog('✅ 心跳测试已启动 - 每1分钟发送一次心跳消息')
        addLog('📡 连接目标: tcp://172.28.8.3:5556')
        addLog('🔄 第一条消息: {"instruction":0,"code":true,"content":{"robotNo":"AGV001","state":true}}')
        addLog('🎯 任务下发测试已同时启用 - 可接收调度系统任务指令(200-203)')
        addLog('⏰ 任务生命周期测试已启用 - 收到任务后45秒发送开始，再45秒发送完成')
        fetchHeartbeatStatus()
      } else {
        addLog(`❌ 启动心跳测试失败: ${data.error}`)
      }
    } catch (error: any) {
      addLog(`启动心跳测试失败: ${error.message}`)
    } finally {
      setHeartbeatLoading(false)
    }
  }

  const stopHeartbeatTest = async () => {
    try {
      setHeartbeatLoading(true)
      const response = await fetch('/api/zmq/heartbeat-test/stop', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      })
      const data = await response.json()

      if (data.success) {
        addLog('✅ 心跳测试已停止')
        addLog('🎯 任务下发测试已同时停止')
        addLog('⏰ 任务生命周期测试已同时停止')
        fetchHeartbeatStatus()
      } else {
        addLog(`停止心跳测试失败: ${data.error}`)
      }
    } catch (error: any) {
      addLog(`停止心跳测试失败: ${error.message}`)
    } finally {
      setHeartbeatLoading(false)
    }
  }

  const fetchHeartbeatStatus = async () => {
    try {
      const response = await fetch('/api/zmq/heartbeat-test/status')
      const data = await response.json()

      if (data.success && data.data) {
        // 解析新的数据结构
        const combinedStatus = data.data as CombinedTestStatus

        // 更新心跳测试状态
        if (combinedStatus.heartbeat) {
          setHeartbeatTest(combinedStatus.heartbeat)
        }

        // 更新任务测试状态
        if (combinedStatus.task) {
          setTaskTest(combinedStatus.task)
        }

        // 更新任务生命周期状态
        if (combinedStatus.taskLifecycle) {
          setTaskLifecycle(combinedStatus.taskLifecycle)
        }
      }
    } catch (error: any) {
      console.error('获取测试状态失败:', error)
    }
  }

  // 手动获取状态
  const fetchStatus = async () => {
    try {
      setError(null)
      const data = await getAutoWatchStatus()
      setStatus(data)
      addLog('状态更新成功')
    } catch (err) {
      const errorMsg = '获取自动看车状态失败: ' + (err as Error).message
      console.error(errorMsg)
      setError(errorMsg)
      addLog(errorMsg)
    }
  }

  // 开始自动看车
  const handleStart = async () => {
    setIsLoading(true)
    setError(null)
    try {
      await startAutoWatch()
      addLog('自动看车功能已启动')
      setStatus(prev => ({ ...prev, isRunning: true }))
    } catch (err) {
      setError('启动失败: ' + (err as Error).message)
      addLog('启动失败: ' + (err as Error).message)
    } finally {
      setIsLoading(false)
    }
  }

  // 停止自动看车
  const handleStop = async () => {
    setIsLoading(true)
    setError(null)
    try {
      await stopAutoWatch()
      addLog('自动看车功能已停止')
      setStatus(prev => ({ ...prev, isRunning: false, isConnected: false }))
    } catch (err) {
      setError('停止失败: ' + (err as Error).message)
      addLog('停止失败: ' + (err as Error).message)
    } finally {
      setIsLoading(false)
    }
  }

  // 一键上线
  const handleOneClickOnline = async () => {
    setIsLoading(true)
    setError(null)
    try {
      const response = await oneClickOnline()
      addLog(`一键上线成功: ${JSON.stringify(response)}`)
      console.log('一键上线成功:', response)
    } catch (err) {
      const errorMessage = (err as Error).message
      setError(`一键上线失败: ${errorMessage}`)
      addLog(`一键上线失败: ${errorMessage}`)
      console.error('一键上线失败:', err)
    } finally {
      setIsLoading(false)
    }
  }

  // 添加日志
  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev].slice(0, 100))
  }

  // 定期刷新状态
  useEffect(() => {
    // 初始加载
    fetchStatus()
    // 不在页面加载时获取心跳状态，只有在用户启动测试后才获取

    // 定期刷新 - 只刷新心跳状态，不刷新看车状态
    const interval = setInterval(() => {
      // 移除自动刷新看车状态，避免不必要的API调用
      // fetchStatus()

      // 只有在心跳测试运行时才获取心跳状态
      if (heartbeatTest.isRunning) {
        fetchHeartbeatStatus()
      }
    }, 5000) // 每5秒刷新一次

    return () => clearInterval(interval)
  }, [heartbeatTest.isRunning])

  // 获取连接状态样式
  const getConnectionStatusStyle = () => {
    switch (status.connectionStatus) {
      case 'connected':
        return 'bg-green-100 text-green-800'
      case 'connecting':
        return 'bg-yellow-100 text-yellow-800'
      case 'error':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  // 格式化运行时间
  const formatRunTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60
    return `${hours}小时 ${minutes}分 ${secs}秒`
  }

  return (
    <div className="p-6 space-y-6">
      {/* 顶部控制区 */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-800">自动看车控制中心</h2>
            <p className="text-gray-600 mt-1">与调度系统协同，自动执行看车任务</p>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className={`px-4 py-2 rounded-full text-sm font-medium ${getConnectionStatusStyle()}`}>
              {status.connectionStatus === 'connected' ? '🟢 已连接' : 
               status.connectionStatus === 'connecting' ? '🟡 连接中' :
               status.connectionStatus === 'error' ? '🔴 连接错误' : '⚪ 未连接'}
            </div>
            
            <button
              onClick={fetchStatus}
              className="px-4 py-2 rounded-lg font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 transition-colors"
            >
              🔄 刷新状态
            </button>
            
            <button
              onClick={handleOneClickOnline}
              disabled={isLoading}
              className={`px-6 py-3 rounded-lg font-medium text-white transition-all transform hover:scale-105 bg-green-500 hover:bg-green-600 ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              {isLoading ? (
                <span className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  处理中...
                </span>
              ) : (
                '🚀 一键上线'
              )}
            </button>
            
            <button
              onClick={status.isRunning ? handleStop : handleStart}
              disabled={isLoading}
              className={`px-8 py-3 rounded-lg font-medium text-white transition-all transform hover:scale-105 ${
                status.isRunning 
                  ? 'bg-red-500 hover:bg-red-600' 
                  : 'bg-blue-500 hover:bg-blue-600'
              } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              {isLoading ? (
                <span className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  处理中...
                </span>
              ) : (
                status.isRunning ? '停止自动看车' : '开始自动看车'
              )}
            </button>
          </div>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
            {error}
          </div>
        )}
      </div>

      {/* ZMQ心跳通信测试面板 */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">🫀 ZMQ心跳通信测试</h3>
        <p className="text-gray-600 text-sm mb-4">独立测试与调度系统的ZMQ心跳通信，每1分钟发送一次心跳</p>

        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-4">
            <button
              onClick={startHeartbeatTest}
              disabled={heartbeatTest.isRunning || heartbeatLoading}
              className={`px-4 py-2 rounded-lg font-medium text-white transition-colors ${
                heartbeatTest.isRunning || heartbeatLoading
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-green-500 hover:bg-green-600'
              }`}
            >
              {heartbeatLoading ? '启动中...' : '▶️ 开始测试'}
            </button>

            <button
              onClick={stopHeartbeatTest}
              disabled={!heartbeatTest.isRunning || heartbeatLoading}
              className={`px-4 py-2 rounded-lg font-medium text-white transition-colors ${
                !heartbeatTest.isRunning || heartbeatLoading
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-red-500 hover:bg-red-600'
              }`}
            >
              {heartbeatLoading ? '停止中...' : '⏹️ 停止测试'}
            </button>
          </div>

          <div className={`px-3 py-1 rounded-full text-sm font-medium ${
            heartbeatTest.isRunning ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
          }`}>
            {heartbeatTest.isRunning ? '🟢 测试中' : '⚪ 已停止'}
          </div>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{heartbeatTest.successCount}</div>
            <div className="text-sm text-gray-600">成功次数</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">{heartbeatTest.failCount}</div>
            <div className="text-sm text-gray-600">失败次数</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {heartbeatTest.lastResult ? `${Math.round(heartbeatTest.lastResult.responseTime / 1000000)}ms` : '-'}
            </div>
            <div className="text-sm text-gray-600">响应时间</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {heartbeatTest.nextTestTime ? new Date(heartbeatTest.nextTestTime).toLocaleTimeString() : '-'}
            </div>
            <div className="text-sm text-gray-600">下次测试</div>
          </div>
        </div>

        {heartbeatTest.lastResult && (
          <div className={`p-3 rounded-lg text-sm ${
            heartbeatTest.lastResult.success
              ? 'bg-green-50 border border-green-200 text-green-800'
              : 'bg-red-50 border border-red-200 text-red-800'
          }`}>
            <div className="flex justify-between items-center">
              <span>
                {heartbeatTest.lastResult.success ? '✅ 最后测试成功' : '❌ 最后测试失败'}
              </span>
              <span className="text-xs">
                {new Date(heartbeatTest.lastResult.timestamp).toLocaleString()}
              </span>
            </div>
            {heartbeatTest.lastResult.error && (
              <div className="mt-2 text-xs opacity-75">
                错误: {heartbeatTest.lastResult.error}
              </div>
            )}
          </div>
        )}
      </div>

      {/* ZMQ任务下发测试面板 */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">🎯 ZMQ任务下发测试</h3>
        <p className="text-gray-600 text-sm mb-4">
          测试接收调度系统的任务下发消息（指令200-203），与心跳测试并行运行
        </p>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
          <div className="text-center">
            <div className={`text-2xl font-bold ${taskTest.taskTestEnabled ? 'text-green-600' : 'text-gray-400'}`}>
              {taskTest.taskTestEnabled ? '✅' : '❌'}
            </div>
            <div className="text-sm text-gray-600">测试状态</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{taskTest.totalTasks}</div>
            <div className="text-sm text-gray-600">总任务数</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{taskTest.successCount}</div>
            <div className="text-sm text-gray-600">成功处理</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">{taskTest.failCount}</div>
            <div className="text-sm text-gray-600">处理失败</div>
          </div>
        </div>

        {/* 最近任务列表 */}
        {taskTest.recentTasks && taskTest.recentTasks.length > 0 && (
          <div className="mt-4">
            <h4 className="text-md font-medium text-gray-700 mb-2">最近接收的任务</h4>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {taskTest.recentTasks.slice(0, 5).map((task, index) => (
                <div key={index} className={`p-3 rounded-lg text-sm border ${
                  task.success
                    ? 'bg-green-50 border-green-200 text-green-800'
                    : 'bg-red-50 border-red-200 text-red-800'
                }`}>
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="font-medium">
                        指令{task.instruction}: {task.taskId || '无任务ID'}
                      </div>
                      <div className="text-xs opacity-75 mt-1">
                        {task.message}
                      </div>
                      {task.laneNos && task.laneNos.length > 0 && (
                        <div className="text-xs opacity-75 mt-1">
                          车道: {task.laneNos.join(', ')}
                        </div>
                      )}
                    </div>
                    <div className="text-xs text-right">
                      <div>{new Date(task.timestamp).toLocaleTimeString()}</div>
                      <div className="mt-1">{Math.round(task.responseTime / 1000000)}ms</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {(!taskTest.recentTasks || taskTest.recentTasks.length === 0) && taskTest.taskTestEnabled && (
          <div className="text-center py-4 text-gray-500">
            等待调度系统发送任务下发消息...
          </div>
        )}
      </div>

      {/* ZMQ任务生命周期测试面板 */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">⏰ ZMQ任务生命周期测试</h3>
        <p className="text-gray-600 text-sm mb-4">
          测试完整的任务生命周期：接收任务下发 → 45秒后发送任务开始(110) → 45秒后发送任务完成(130)
        </p>

        {/* 当前任务状态 */}
        <div className="mb-4 p-4 rounded-lg bg-gray-50">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-lg font-bold text-blue-600">
                {taskLifecycle.currentTaskId || '无'}
              </div>
              <div className="text-sm text-gray-600">当前任务ID</div>
            </div>
            <div className="text-center">
              <div className={`text-lg font-bold ${
                taskLifecycle.taskState === 'none' ? 'text-gray-400' :
                taskLifecycle.taskState === 'received' ? 'text-yellow-600' :
                taskLifecycle.taskState === 'started' ? 'text-blue-600' :
                taskLifecycle.taskState === 'completed' ? 'text-green-600' : 'text-gray-400'
              }`}>
                {taskLifecycle.taskState === 'none' ? '无任务' :
                 taskLifecycle.taskState === 'received' ? '已接收' :
                 taskLifecycle.taskState === 'started' ? '已开始' :
                 taskLifecycle.taskState === 'completed' ? '已完成' : '未知'}
              </div>
              <div className="text-sm text-gray-600">任务状态</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-purple-600">
                {taskLifecycle.taskStartTime ? new Date(taskLifecycle.taskStartTime).toLocaleTimeString() : '-'}
              </div>
              <div className="text-sm text-gray-600">开始时间</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-green-600">
                {taskLifecycle.taskCompleteTime ? new Date(taskLifecycle.taskCompleteTime).toLocaleTimeString() : '-'}
              </div>
              <div className="text-sm text-gray-600">完成时间</div>
            </div>
          </div>
        </div>

        {/* 任务开始通知历史 */}
        {taskLifecycle.startNotifications && taskLifecycle.startNotifications.length > 0 && (
          <div className="mb-4">
            <h4 className="text-md font-medium text-gray-700 mb-2">任务开始通知历史 (指令110)</h4>
            <div className="space-y-2 max-h-32 overflow-y-auto">
              {taskLifecycle.startNotifications.slice(-3).map((notification, index) => (
                <div key={index} className={`p-3 rounded-lg text-sm border ${
                  notification.success
                    ? 'bg-green-50 border-green-200 text-green-800'
                    : 'bg-red-50 border-red-200 text-red-800'
                }`}>
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="font-medium">
                        {notification.success ? '✅' : '❌'} 任务开始通知: {notification.taskId}
                      </div>
                      <div className="text-xs opacity-75 mt-1">
                        {notification.message}
                      </div>
                    </div>
                    <div className="text-xs text-right">
                      <div>{new Date(notification.timestamp).toLocaleTimeString()}</div>
                      <div className="mt-1">{Math.round(notification.responseTime / 1000000)}ms</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 任务完成通知历史 */}
        {taskLifecycle.completeNotifications && taskLifecycle.completeNotifications.length > 0 && (
          <div className="mb-4">
            <h4 className="text-md font-medium text-gray-700 mb-2">任务完成通知历史 (指令130)</h4>
            <div className="space-y-2 max-h-32 overflow-y-auto">
              {taskLifecycle.completeNotifications.slice(-3).map((notification, index) => (
                <div key={index} className={`p-3 rounded-lg text-sm border ${
                  notification.success
                    ? 'bg-green-50 border-green-200 text-green-800'
                    : 'bg-red-50 border-red-200 text-red-800'
                }`}>
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="font-medium">
                        {notification.success ? '✅' : '❌'} 任务完成通知: {notification.taskId}
                      </div>
                      <div className="text-xs opacity-75 mt-1">
                        {notification.message}
                      </div>
                    </div>
                    <div className="text-xs text-right">
                      <div>{new Date(notification.timestamp).toLocaleTimeString()}</div>
                      <div className="mt-1">{Math.round(notification.responseTime / 1000000)}ms</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 等待状态提示 */}
        {taskLifecycle.taskState === 'none' && (
          <div className="text-center py-4 text-gray-500">
            等待调度系统发送任务下发消息以启动生命周期测试...
          </div>
        )}

        {taskLifecycle.taskState === 'received' && (
          <div className="text-center py-4 text-yellow-600">
            ⏳ 任务已接收，等待45秒后发送任务开始通知...
          </div>
        )}

        {taskLifecycle.taskState === 'started' && (
          <div className="text-center py-4 text-blue-600">
            ⏳ 任务已开始，等待45秒后发送任务完成通知...
          </div>
        )}

        {taskLifecycle.taskState === 'completed' && (
          <div className="text-center py-4 text-green-600">
            🎉 任务生命周期已完成！
          </div>
        )}
      </div>

      {/* 状态信息区 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* 机器人信息 */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">机器人信息</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">机器人编号</span>
              <span className="font-medium">{status.robotNo}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">运行状态</span>
              <span className={`font-medium ${status.isRunning ? 'text-green-600' : 'text-gray-500'}`}>
                {status.isRunning ? '运行中' : '已停止'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">最后心跳</span>
              <span className="font-medium text-sm">
                {status.lastHeartbeat || '无'}
              </span>
            </div>
          </div>
        </div>

        {/* 当前任务 */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">当前任务</h3>
          {status.currentTask ? (
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">任务ID</span>
                <span className="font-medium">{status.currentTask.taskId}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">目标机器</span>
                <span className="font-medium">{status.currentTask.machineNo}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">任务类型</span>
                <span className="font-medium">{status.currentTask.taskType}</span>
              </div>
              <div className="mt-4">
                <div className="flex justify-between mb-1">
                  <span className="text-gray-600">进度</span>
                  <span className="font-medium">{status.currentTask.progress}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${status.currentTask.progress}%` }}
                  />
                </div>
              </div>
            </div>
          ) : (
            <div className="text-gray-500 text-center py-8">
              暂无执行中的任务
            </div>
          )}
        </div>

        {/* 统计信息 */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">任务统计</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">总任务数</span>
              <span className="font-medium">{status.statistics.totalTasks}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">已完成</span>
              <span className="font-medium text-green-600">{status.statistics.completedTasks}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">失败任务</span>
              <span className="font-medium text-red-600">{status.statistics.failedTasks}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">总运行时间</span>
              <span className="font-medium text-sm">
                {formatRunTime(status.statistics.totalRunTime)}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* 日志区域 */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">运行日志</h3>
        <div className="bg-gray-50 rounded-lg p-4 h-64 overflow-y-auto">
          {logs.length > 0 ? (
            <div className="space-y-1">
              {logs.map((log, index) => (
                <div key={index} className="text-sm text-gray-700 font-mono">
                  {log}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-gray-500 text-center py-8">
              暂无日志信息
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default AutoWatchPanel
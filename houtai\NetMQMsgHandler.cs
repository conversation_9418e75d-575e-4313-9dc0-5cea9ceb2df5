﻿using BackendSchedulingSystem.Const;
using BackendSchedulingSystem.Dictionaries.Enums;
using BackendSchedulingSystem.Helpers.NetMQ;
using BackendSchedulingSystem.Models;
using BackendSchedulingSystem.Models.Outputs;
using BackendSchedulingSystem.Utilities;

namespace BackendSchedulingSystem.Helpers.Handlers
{
    /// <summary>
    /// NetMQ 消息整理 发送
    /// </summary>
    public class NetMQMsgHandler
    {
        /// <summary>
        /// 机器人任务下发
        /// </summary>
        /// <param name="robot"></param>
        /// <param name="robotTask"></param>
        /// <returns></returns>
        public async Task<bool> RobotTaskAssignment(Robot robot, RobotTask robotTask)
        {
            try
            {
                if (robotTask.RobotTaskState != RobotTaskStatusEnum.Unassigned)
                {
                    NLogHelper.Warn("机器人任务下发", $"机器人任务【{robotTask.RobotTaskId}】状态不对，{robotTask.StateToString()}");
                    return false;
                }

                if (!robot.IdleState)
                {
                    NLogHelper.Warn("机器人任务下发", $"机器人【{robot.RobotNo}】状态不对，{robot.StateToString()}");
                    return false;
                }

                if (!robotTask.AllFrameState)
                {
                    foreach (string laneNo in robotTask.LaneNos.ToList())
                    {
                        if (!robotTask.FrameStatus[laneNo])
                        {
                            robotTask.DisconnectionList.TryRemove(laneNo, out _);
                            robotTask.RemainingDisconnectionList.TryRemove(laneNo, out _);
                        }
                    }
                }

                RobotTaskAssignmentOutput content = new()
                {
                    taskId = robotTask.RobotTaskId,
                    robotNo = robot.RobotNo,
                    laneNos = robotTask.LaneNos,
                    remark = string.Empty,
                };

                string response = NetMQMsgHelper.Success(InstructionEnum.TaskAssign, content);
                NLogHelper.Info("机器人任务下发", $"任务下发：{response}");

                bool isPush = await Task.Run(() => RequestNetMQHelper.PushMessageAsync(robot, response));

                if (isPush)
                {
                    robot.CurrentRobotTaskId = robotTask.RobotTaskId;
                    robot.CurrentLaneNos = robotTask.LaneNos;

                    robotTask.RobotNo = robot.RobotNo;
                    robotTask.RobotTaskState = RobotTaskStatusEnum.NotStarted;

                    AppConst.UpdateRobot(robot);
                    AppConst.UpdateRobotTask(robotTask);
                }

                return isPush;
            }
            catch (Exception ex)
            {
                NLogHelper.Fatal("机器人任务下发", "发生未知异常", ex);
                return false;
            }
        }

        /// <summary>
        /// 等待机器人任务
        /// </summary>
        /// <param name="robot"></param>
        /// <param name="robotTask"></param>
        /// <returns></returns>
        public async Task<bool> WaitRobotTask(Robot robot)
        {
            try
            {
                RobotTaskAssignmentOutput content = new()
                {
                    robotNo = robot.RobotNo,
                    remark = string.Empty,
                };

                string response = NetMQMsgHelper.Success(InstructionEnum.WaitingForTask, content);
                NLogHelper.Info("等待机器人任务", response);

                bool isPush = await Task.Run(() => RequestNetMQHelper.PushMessageAsync(robot, response));

                if (isPush)
                {
                    robot.CurrentRobotTaskId = null;
                    robot.CurrentLaneNos = [];

                    AppConst.UpdateRobot(robot);
                }
                return isPush;
            }
            catch (Exception ex)
            {
                NLogHelper.Fatal("开始机器人任务", "发生未知异常", ex);
                return false;
            }
        }

        /// <summary>
        /// 无任务
        /// </summary>
        /// <param name="robot"></param>
        /// <returns></returns>
        public async Task<bool> NoRobotTask(Robot robot)
        {
            try
            {
                RobotTaskAssignmentOutput content = new()
                {
                    robotNo = robot.RobotNo,
                    remark = string.Empty,
                };

                string response = NetMQMsgHelper.Success(InstructionEnum.NoTask, content);
                NLogHelper.Info("无任务", response);

                bool isPush = await Task.Run(() => RequestNetMQHelper.PushMessageAsync(robot, response));

                if (isPush)
                {
                    AppConst.UpdateRobot(robot);
                }
                return isPush;
            }
            catch (Exception ex)
            {
                NLogHelper.Fatal("无任务", "发生未知异常", ex);
                return false;
            }
        }

        /// <summary>
        /// 继续机器人任务
        /// </summary>
        /// <param name="robot"></param>
        /// <param name="robotTask"></param>
        /// <returns></returns>
        public async Task<bool> ContinueRobotTask(Robot robot, RobotTask robotTask)
        {
            try
            {
                if (!robotTask.WaitingTaskStartState)
                {
                    NLogHelper.Warn("继续机器人任务", $"机器人任务【{robotTask.RobotTaskId}】状态不对，{robotTask.StateToString()}");
                    return false;
                }

                if (!robot.WaitingTaskStartState)
                {
                    NLogHelper.Warn("继续机器人任务", $"机器人【{robot.RobotNo}】状态不对，{robot.StateToString()}");
                    return false;
                }

                if (!robotTask.AllFrameState)
                {
                    foreach (string laneNo in robotTask.LaneNos.ToList())
                    {
                        if (!robotTask.FrameStatus[laneNo])
                        {
                            robotTask.DisconnectionList.TryRemove(laneNo, out _);
                            robotTask.RemainingDisconnectionList.TryRemove(laneNo, out _);
                        }
                    }
                }

                RobotTaskAssignmentOutput content = new()
                {
                    taskId = robotTask.RobotTaskId,
                    robotNo = robot.RobotNo,
                    laneNos = robotTask.LaneNos,
                    remark = string.Empty,
                };

                string response = NetMQMsgHelper.Success(InstructionEnum.ContinueTask, content);
                NLogHelper.Info("继续机器人任务", $"任务开始：{response}");

                bool isPush = await Task.Run(() => RequestNetMQHelper.PushMessageAsync(robot, response));

                if (isPush)
                {
                    robot.CurrentRobotTaskId = robotTask.RobotTaskId;
                    robot.CurrentLaneNos = robotTask.LaneNos;

                    robotTask.RobotNo = robot.RobotNo;
                    robotTask.RobotTaskState = RobotTaskStatusEnum.NotStarted;

                    AppConst.UpdateRobotTask(robotTask);
                }
                return isPush;
            }
            catch (Exception ex)
            {
                NLogHelper.Fatal("继续机器人任务", "发生未知异常", ex);
                return false;
            }
        }

        /// <summary>
        /// 延时发送机器人心跳
        /// </summary>
        /// <param name="robotNo"></param>
        /// <returns></returns>
        public void SendWithDelayRobotHeartbeat(string robotNo)
        {
            try
            {
                double heartbeatIntervalTime = AppSettings.CalculateRule.Robot.HeartbeatIntervalTime ?? 1;

                TimerUtility timer = new(
                    callback: async () => await SendRobotHeartbeat(robotNo),
                    interval: heartbeatIntervalTime * 60 * 1000,
                    autoReset: false
                );

                timer.Start();
            }
            catch (Exception ex)
            {
                NLogHelper.Fatal("发送机器人心跳", "发生未知异常", ex);
            }
        }

        /// <summary>
        /// 发送机器人心跳
        /// </summary>
        /// <param name="robot"></param>
        /// <returns></returns>
        public async Task<bool> SendRobotHeartbeat(string robotNo)
        {
            try
            {
                Robot? robot = AppConst.GetRobotByNo(robotNo);
                if (robot == null)
                {
                    return false;
                }

                RobotHeartbeatOutput output = new()
                {
                    robotNo = robot.RobotNo,
                    state = !robot.HeartbeatState,
                };

                robot.SendHeartbeatTime = DateTime.Now;
                robot.LastHeartbeatState = robot.HeartbeatState;
                robot.HeartbeatState = output.state;

                // 创建心跳消息
                string response = NetMQMsgHelper.Success(InstructionEnum.Heartbeat, output);

                // 发送心跳消息并处理响应
                bool isPush = await Task.Run(() => RequestNetMQHelper.PushMessageAsync(robot, response));

                if (!isPush)
                {
                    NLogHelper.Warn("发送机器人心跳", $"机器人【{robot.RobotNo}】发送心跳消息，并未接收到响应");
                }

                AppConst.UpdateRobot(robot);
                return isPush;
            }
            catch (Exception ex)
            {
                NLogHelper.Fatal("发送机器人心跳", "发生未知异常", ex);
                return false;
            }
        }
    }
}

package api

import (
	"net/http"
	"time"

	"github.com/user/agv_nav/internal/zmq"
	"github.com/user/agv_nav/pkg/logger"
	zmqpkg "github.com/user/agv_nav/pkg/zmq"
)

// heartbeatTester 全局心跳测试器实例
var heartbeatTester *zmq.HeartbeatTester

// initHeartbeatTester 初始化心跳测试器
func (s *Server) initHeartbeatTester() error {
	if heartbeatTester != nil {
		return nil // 已经初始化
	}

	// 创建ZMQ配置
	config := zmqpkg.DefaultConfig()
	config.RequesterEndpoint = "tcp://172.28.8.3:5556" // 连接到调度系统
	config.ResponderEndpoint = "tcp://*:5557"          // 本机监听端口，供调度系统连接
	config.SendTimeout = 5 * time.Second
	config.RecvTimeout = 5 * time.Second

	// 创建请求者
	requester, err := zmqpkg.NewRequester(config)
	if err != nil {
		return err
	}

	// 创建响应者
	responder, err := zmqpkg.NewResponder(config)
	if err != nil {
		return err
	}

	// 加载系统配置
	sysConfig, err := zmq.LoadSystemConfig("data/task_config.json")
	if err != nil {
		// 使用默认配置
		sysConfig = zmq.DefaultSystemConfig()
	}

	// 创建日志器
	testLogger := logger.GetModuleLogger("zmq-test")

	// 创建心跳测试器
	heartbeatTester = zmq.NewHeartbeatTester(requester, responder, sysConfig, testLogger)

	return nil
}

// handleStartHeartbeatTest 处理启动心跳测试请求
func (s *Server) handleStartHeartbeatTest(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		s.enableCORS(w)
		w.WriteHeader(http.StatusOK)
		return
	}

	apiLogger := logger.GetModuleLogger("api")
	startTime := time.Now()
	clientIP := r.RemoteAddr
	apiLogger.Info("启动ZMQ心跳测试API请求", "method", r.Method, "path", r.URL.Path, "clientIP", clientIP)

	// 初始化心跳测试器
	if err := s.initHeartbeatTester(); err != nil {
		apiLogger.Error("初始化心跳测试器失败", "clientIP", clientIP, "error", err, "duration", time.Since(startTime))
		s.respondError(w, "初始化心跳测试器失败: "+err.Error(), http.StatusInternalServerError)
		return
	}

	// 检查是否已经在运行
	if heartbeatTester.IsRunning() {
		apiLogger.Info("心跳测试已在运行", "clientIP", clientIP)
		s.respondJSON(w, map[string]interface{}{
			"success": true,
			"message": "心跳测试已在运行",
			"data": map[string]interface{}{
				"interval":  "1分钟",
				"startTime": time.Now().Format("2006-01-02 15:04:05"),
			},
		})
		return
	}

	// 启动心跳测试
	err := heartbeatTester.Start()
	if err != nil {
		apiLogger.Error("启动心跳测试失败", "clientIP", clientIP, "error", err, "duration", time.Since(startTime))
		s.respondError(w, "启动心跳测试失败: "+err.Error(), http.StatusBadRequest)
		return
	}

	apiLogger.Info("ZMQ心跳测试启动成功", "clientIP", clientIP, "duration", time.Since(startTime))
	s.respondJSON(w, map[string]interface{}{
		"success": true,
		"message": "心跳测试已启动，每1分钟发送一次心跳",
		"data": map[string]interface{}{
			"interval":  "1分钟",
			"startTime": time.Now().Format("2006-01-02 15:04:05"),
		},
	})
}

// handleStopHeartbeatTest 处理停止心跳测试请求
func (s *Server) handleStopHeartbeatTest(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		s.enableCORS(w)
		w.WriteHeader(http.StatusOK)
		return
	}

	apiLogger := logger.GetModuleLogger("api")
	startTime := time.Now()
	clientIP := r.RemoteAddr
	apiLogger.Info("停止ZMQ心跳测试API请求", "method", r.Method, "path", r.URL.Path, "clientIP", clientIP)

	// 检查测试器是否存在
	if heartbeatTester == nil {
		apiLogger.Warn("心跳测试器未初始化", "clientIP", clientIP)
		s.respondError(w, "心跳测试器未初始化", http.StatusBadRequest)
		return
	}

	// 停止心跳测试
	err := heartbeatTester.Stop()
	if err != nil {
		apiLogger.Error("停止心跳测试失败", "clientIP", clientIP, "error", err, "duration", time.Since(startTime))
		s.respondError(w, "停止心跳测试失败: "+err.Error(), http.StatusBadRequest)
		return
	}

	apiLogger.Info("ZMQ心跳测试停止成功", "clientIP", clientIP, "duration", time.Since(startTime))
	s.respondJSON(w, map[string]interface{}{
		"success": true,
		"message": "心跳测试已停止",
		"data": map[string]interface{}{
			"stopTime": time.Now().Format("2006-01-02 15:04:05"),
		},
	})
}

// handleGetHeartbeatTestStatus 处理获取心跳测试状态请求
func (s *Server) handleGetHeartbeatTestStatus(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		s.enableCORS(w)
		w.WriteHeader(http.StatusOK)
		return
	}

	apiLogger := logger.GetModuleLogger("api")
	clientIP := r.RemoteAddr
	apiLogger.Debug("获取ZMQ心跳测试状态API请求", "method", r.Method, "path", r.URL.Path, "clientIP", clientIP)

	// 检查测试器是否存在
	if heartbeatTester == nil {
		apiLogger.Debug("心跳测试器未初始化，返回默认状态", "clientIP", clientIP)
		s.respondJSON(w, map[string]interface{}{
			"success": true,
			"message": "获取心跳测试状态成功",
			"data": map[string]interface{}{
				"isRunning":    false,
				"successCount": 0,
				"failCount":    0,
				"lastResult":   nil,
				"startTime":    nil,
				"nextTestTime": nil,
			},
		})
		return
	}

	// 获取心跳测试状态
	heartbeatStatus := heartbeatTester.GetStatus()

	// 获取任务测试状态
	taskStatus := heartbeatTester.GetTaskTestStatus()

	// 获取任务生命周期状态
	taskLifecycleStatus := heartbeatTester.GetTaskLifecycleStatus()

	// 合并状态信息
	combinedStatus := map[string]interface{}{
		"heartbeat":     heartbeatStatus,
		"task":          taskStatus,
		"taskLifecycle": taskLifecycleStatus,
	}

	apiLogger.Debug("获取ZMQ测试状态成功",
		"clientIP", clientIP,
		"heartbeatRunning", heartbeatStatus.IsRunning,
		"heartbeatSuccess", heartbeatStatus.SuccessCount,
		"taskTestEnabled", taskStatus["taskTestEnabled"],
		"totalTasks", taskStatus["totalTasks"],
		"currentTaskId", taskLifecycleStatus.CurrentTaskId,
		"taskState", taskLifecycleStatus.TaskState)

	s.respondJSON(w, map[string]interface{}{
		"success": true,
		"message": "获取ZMQ测试状态成功（心跳+任务下发+生命周期）",
		"data":    combinedStatus,
	})
}

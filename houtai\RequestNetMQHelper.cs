﻿using BackendSchedulingSystem.Const;
using BackendSchedulingSystem.Helpers.Handlers;
using BackendSchedulingSystem.Models;
using NetMQ;
using NetMQ.Sockets;
using System.Collections.Concurrent;

namespace BackendSchedulingSystem.Helpers.NetMQ
{
    /// <summary>
    /// NetMQ 工具类 - 发送消息并接收响应
    /// 提供基于NetMQ的请求-响应模式通信功能，支持多机器人连接管理、心跳检测和自动重连
    /// </summary>
    public static class RequestNetMQHelper
    {
        private static string _logName = "NetMQ 工具类 - 发送消息并接收响应";

        /// <summary>
        /// 使用ConcurrentDictionary确保线程安全，存储地址与RequestSocket的映射
        /// </summary>
        private static readonly ConcurrentDictionary<string, RequestSocket> _requestSockets = new();

        /// <summary>
        /// 记录每个地址的连续失败次数，用于故障处理
        /// </summary>
        private static readonly ConcurrentDictionary<string, int> _consecutiveFailures = new();

        /// <summary>
        /// 初始化锁，确保初始化操作的线程安全
        /// </summary>
        private static readonly SemaphoreSlim _initializationLock = new(1, 1);

        /// <summary>
        /// 初始化状态标志
        /// </summary>
        private static bool _isInitialized = false;

        /// <summary>
        /// NetMQ服务处理器，用于处理返回的消息
        /// </summary>
        private static readonly NetMQHandler _netMQHandler = new();

        /// <summary>
        /// 初始化客户端连接
        /// 扫描配置中的所有机器人地址并建立连接
        /// </summary>
        public static async Task InitializeAsync()
        {
            await _initializationLock.WaitAsync();

            string logName = _logName + Environment.NewLine + "初始化客户端连接 扫描配置中的所有机器人地址并建立连接";
            try
            {
                if (_isInitialized)
                {
                    NLogHelper.Info(logName, "已初始化，无需重复初始化");
                    return;
                }

                // 为配置中的所有机器人创建连接
                foreach (Robot robot in AppConst.GetRobotsByCondition())
                {
                    EnsureConnection(robot.Address);
                }

                _isInitialized = true;
                NLogHelper.Info(logName, "初始化完成");
            }
            catch (Exception ex)
            {
                NLogHelper.Error(logName, "发生未知异常", ex);
                throw;
            }
            finally
            {
                _initializationLock.Release();
            }
        }

        /// <summary>
        /// 确保与指定地址的连接存在
        /// 如果连接不存在或已断开，则创建新连接
        /// </summary>
        /// <param name="address"></param>
        private static void EnsureConnection(string address)
        {
            string logName = _logName + Environment.NewLine + "确保与指定地址的连接存在 如果连接不存在或已断开，则创建新连接";

            if (!_requestSockets.TryGetValue(address, out RequestSocket? socket) || socket == null || socket.IsDisposed)
            {
                // 关闭并移除可能存在的旧连接，防止资源泄漏
                CloseSocket(address);

                NLogHelper.Info(logName, $"正在连接到客户端: {address}");

                try
                {
                    // 创建新的RequestSocket并连接到指定地址
                    RequestSocket newSocket = new();
                    newSocket.Connect(address);

                    // 使用TryAdd确保线程安全地添加新连接
                    if (_requestSockets.TryAdd(address, newSocket))
                    {
                        // 初始化连续失败次数为0
                        _consecutiveFailures.TryAdd(address, 0);
                        NLogHelper.Info(logName, $"成功连接到客户端: {address}");
                    }
                    else
                    {
                        // 添加失败时释放资源，避免资源泄漏
                        newSocket.Close();
                        newSocket.Dispose();
                        NLogHelper.Info(logName, $"添加客户端连接到集合失败: {address}");
                    }
                }
                catch (Exception ex)
                {
                    NLogHelper.Error(logName, "发生未知异常", ex);
                    throw;
                }
            }
        }

        /// <summary>
        /// 推送消息到指定地址
        /// 发送消息并等待响应，支持超时处理和自动重试
        /// </summary>
        /// <param name="robot"></param>
        /// <param name="response"></param>
        /// <returns></returns>
        /// <exception cref="TimeoutException"></exception>
        /// <exception cref="InvalidOperationException"></exception>
        public static async Task<bool> PushMessageAsync(Robot robot, string response)
        {
            string logName = _logName + Environment.NewLine + "推送消息到指定地址 发送消息并等待响应，支持超时处理和自动重试";

            // 获取配置中的最大重试频率和超时时间
            int netMQMaxFrequency = AppSettings.CalculateRule.Robot.NetMQMaxFrequency ?? 5;

            double netMQTimeoutTime = AppSettings.CalculateRule.Robot.NetMQTimeoutTime ?? 5;

            // 如果未初始化，则先初始化
            if (!_isInitialized)
            {
                await InitializeAsync();
            }

            string address = robot.Address;

            try
            {
                // 获取当前连续失败次数
                int failureCount = _consecutiveFailures.GetOrAdd(address, 0);

                // 如果连续失败次数超过最大重试频率，暂停发送
                if (failureCount >= netMQMaxFrequency)
                {
                    // 关闭并移除失败的连接，准备重新连接
                    CloseSocket(address);

                    NLogHelper.Info(logName, $"客户端【{address}】已达到最大重试次数，暂停发送");
                }

                // 确保连接存在
                EnsureConnection(address);

                // 获取或创建连接
                if (_requestSockets.TryGetValue(address, out RequestSocket? socket) && socket != null)
                {
                    // 发送消息
                    socket.SendFrame(response);
                    NLogHelper.NetMQRequestSendRecord(address, response);

                    // 使用带超时的接收方法，避免长时间阻塞
                    if (socket.TryReceiveFrameString(TimeSpan.FromSeconds(netMQTimeoutTime * 60), out string? request))
                    {
                        // 接收响应
                        NLogHelper.NetMQRequestReceiveRecord(address, request);

                        // 重置连续失败计数
                        _consecutiveFailures[address] = 0;

                        // 处理返回的消息
                        return _netMQHandler.ProcessActivelyReceivedMessage(request);
                    }
                    else
                    {
                        throw new TimeoutException("接收响应超时");
                    }
                }
                else
                {
                    throw new InvalidOperationException($"找不到客户端{address}的连接");
                }
            }
            catch (Exception ex)
            {
                // 增加连续失败计数
                int newFailureCount = _consecutiveFailures.AddOrUpdate(address, 1, (key, oldValue) => oldValue + 1);

                NLogHelper.Warn(logName, $"向客户端【{address}】发送消息失败(尝试 {newFailureCount} / {netMQMaxFrequency})", ex);

                // 超过最大重试次数时通知
                if (newFailureCount >= netMQMaxFrequency)
                {
                    // 关闭并移除失败的连接，准备重新连接
                    CloseSocket(address);

                    NLogHelper.Fatal(logName, $"客户端【{address}】已断开连接，达到最大重试次数", ex);
                    // 可以添加额外的通知机制，如告警
                }
                return false;
            }
        }

        /// <summary>
        /// 关闭并清理指定地址的连接
        /// 安全地释放Socket资源并从字典中移除
        /// </summary>
        /// <param name="address">目标地址</param>
        private static void CloseSocket(string address)
        {
            string logName = _logName + Environment.NewLine + "关闭并清理指定地址的连接 安全地释放Socket资源并从字典中移除";

            if (_requestSockets.TryRemove(address, out RequestSocket? socket))
            {
                try
                {
                    _consecutiveFailures.TryRemove(address, out _);

                    // 关闭并释放Socket资源
                    socket?.Close();
                    socket?.Dispose();
                    NLogHelper.Info(logName, $"已关闭客户端【{address}】的连接");
                }
                catch (Exception ex)
                {
                    NLogHelper.Fatal(logName, $"关闭客户端【{address}】的连接时发生未知异常", ex);
                }
            }
        }

        /// <summary>
        /// 异步关闭所有连接并释放资源
        /// 安全地清理所有连接和资源，用于系统关闭时
        /// </summary>
        public static async Task ShutdownAsync()
        {
            await _initializationLock.WaitAsync();

            string logName = _logName + Environment.NewLine + "异步关闭所有连接并释放资源 安全地清理所有连接和资源，用于系统关闭时";
            try
            {
                if (!_isInitialized)
                {
                    NLogHelper.Info(logName, "RequestNetMQHelper未初始化，无需关闭");
                    return;
                }

                NLogHelper.Info(logName, "开始异步关闭RequestNetMQHelper...");

                // 创建一个任务列表来并行关闭所有连接，提高关闭效率
                List<Task> closeTasks = _requestSockets.Keys.Select(async address =>
                {
                    try
                    {
                        if (_requestSockets.TryRemove(address, out RequestSocket? socket))
                        {
                            // 使用Task.Run在后台线程上执行阻塞操作，避免阻塞主线程
                            await Task.Run(() =>
                            {
                                socket?.Close();
                                socket?.Dispose();
                                NLogHelper.Info(logName, $"已关闭客户端【{address}】的连接");
                            });
                        }
                    }
                    catch (Exception ex)
                    {
                        NLogHelper.Error(logName, $"关闭客户端【{address}】的连接时发生未知异常", ex);
                    }
                }).ToList();

                // 等待所有连接关闭完成
                await Task.WhenAll(closeTasks);

                // 清理字典
                _requestSockets.Clear();
                _consecutiveFailures.Clear();

                // 重置初始化状态
                _isInitialized = false;

                NLogHelper.Info(logName, "已成功异步关闭");
            }
            catch (Exception ex)
            {
                NLogHelper.Fatal(logName, "发生未知异常", ex);
                throw;
            }
            finally
            {
                _initializationLock.Release();
            }
        }
    }
}
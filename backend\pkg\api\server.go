package api

import (
	"encoding/json"
	"log"
	"net/http"
	"sync"
	"time"

	"github.com/gorilla/mux"
	"github.com/gorilla/websocket"
	"github.com/user/agv_nav/internal/auto_watch"
	"github.com/user/agv_nav/internal/cache"
	"github.com/user/agv_nav/internal/task"
	"github.com/user/agv_nav/pkg/agv"
	"github.com/user/agv_nav/pkg/plc"
	agv_test "github.com/user/agv_nav/test/agv_test"
	plc_test "github.com/user/agv_nav/test/plc_test"
)

// Server 表示API服务器
type Server struct {
	agvController *agv.Controller
	plcController *plc.Controller
	plcTestAPI    *plc_test.PLCTestAPI
	agvTestAPI    *agv_test.AGVTestAPIHandler
	clients       map[*websocket.Conn]bool
	clientsMutex  sync.RWMutex
	clientLocks   map[*websocket.Conn]*sync.Mutex // 每个客户端连接的写入锁
	upgrader      websocket.Upgrader

	// 防抖相关字段
	lastStatusBroadcast  time.Time
	lastTaskBroadcast    time.Time
	broadcastMutex       sync.Mutex
	minBroadcastInterval time.Duration // 最小广播间隔
}

// 全局服务器实例，用于任务状态广播
var globalServer *Server

// NewServer 创建新的API服务器
func NewServer() *Server {
	upgrader := websocket.Upgrader{
		CheckOrigin: func(r *http.Request) bool {
			// 允许所有来源（开发环境）
			return true
		},
	}

	server := &Server{
		clients:              make(map[*websocket.Conn]bool),
		clientLocks:          make(map[*websocket.Conn]*sync.Mutex),
		upgrader:             upgrader,
		minBroadcastInterval: 500 * time.Millisecond, // 最小500毫秒间隔
	}

	// 创建控制器
	server.agvController = agv.NewController()
	server.plcController = plc.NewController()

	// 创建PLC测试API
	server.plcTestAPI = plc_test.NewPLCTestAPI(server.plcController)

	// 创建AGV测试API（传递主程序的AGV控制器）
	agvTestAPI, err := agv_test.NewAGVTestAPIHandler(server.agvController)
	if err != nil {
		log.Printf("警告：创建AGV测试API失败: %v", err)
		server.agvTestAPI = nil
	} else {
		server.agvTestAPI = agvTestAPI
		log.Printf("AGV测试API初始化成功（复用主程序AGV控制器）")
	}

	// 设置全局服务器实例
	globalServer = server

	// 初始化任务管理器
	task.InitTaskManager(server.agvController, server.plcController)

	// 设置任务状态广播回调
	task.SetTaskStatusBroadcastCallback(func() {
		server.BroadcastTaskStatus()
	})

	// 设置流程状态广播回调
	task.SetFlowStatusBroadcastCallback(func() {
		server.BroadcastFlowStatus()
	})

	// 初始化缓存管理器
	if err := cache.InitGlobalManager(); err != nil {
		log.Printf("警告：初始化缓存管理器失败: %v", err)
	}

	// 初始化自动看车服务
	autoWatchService, err := auto_watch.NewAutoWatchService(server.agvController, server.plcController)
	if err != nil {
		log.Printf("警告：创建自动看车服务失败: %v", err)
	} else {
		SetAutoWatchService(autoWatchService)
		log.Printf("自动看车服务初始化成功")
	}

	return server
}

// BroadcastStatus 广播AGV状态给所有连接的客户端
func (s *Server) BroadcastStatus() {
	// 防抖检查
	s.broadcastMutex.Lock()
	now := time.Now()
	if now.Sub(s.lastStatusBroadcast) < s.minBroadcastInterval {
		s.broadcastMutex.Unlock()
		return // 跳过过于频繁的广播
	}
	s.lastStatusBroadcast = now
	s.broadcastMutex.Unlock()

	status := s.agvController.GetStatus()
	if status == nil {
		return
	}

	statusData, err := json.Marshal(map[string]interface{}{
		"type":   "agv_status",
		"status": status,
	})
	if err != nil {
		log.Printf("编码状态数据失败: %v", err)
		return
	}

	s.clientsMutex.RLock()
	// 复制客户端列表和锁引用，避免在持有读锁时进行写操作
	clientList := make([]*websocket.Conn, 0, len(s.clients))
	lockList := make([]*sync.Mutex, 0, len(s.clients))
	for client := range s.clients {
		if lock, exists := s.clientLocks[client]; exists {
			clientList = append(clientList, client)
			lockList = append(lockList, lock)
		}
	}
	s.clientsMutex.RUnlock()

	// 向每个客户端发送消息，使用各自的锁保护写入
	for i, client := range clientList {
		lock := lockList[i]
		go func(c *websocket.Conn, l *sync.Mutex) {
			l.Lock()
			defer l.Unlock()

			err := c.WriteMessage(websocket.TextMessage, statusData)
			if err != nil {
				log.Printf("发送WebSocket消息失败: %v", err)
				// 标记连接需要清理
				s.clientsMutex.Lock()
				if _, exists := s.clients[c]; exists {
					delete(s.clients, c)
					delete(s.clientLocks, c)
					c.Close()
				}
				s.clientsMutex.Unlock()
			}
		}(client, lock)
	}
}

// BroadcastTaskStatus 广播任务状态给所有连接的客户端
func (s *Server) BroadcastTaskStatus() {
	// 防抖检查
	s.broadcastMutex.Lock()
	now := time.Now()
	if now.Sub(s.lastTaskBroadcast) < s.minBroadcastInterval {
		s.broadcastMutex.Unlock()
		return // 跳过过于频繁的广播
	}
	s.lastTaskBroadcast = now
	s.broadcastMutex.Unlock()

	status := task.GetTaskStatus()

	statusData, err := json.Marshal(map[string]interface{}{
		"type":   "task_status",
		"status": status,
	})
	if err != nil {
		log.Printf("编码任务状态数据失败: %v", err)
		return
	}

	s.clientsMutex.RLock()
	// 复制客户端列表和锁引用，避免在持有读锁时进行写操作
	clientList := make([]*websocket.Conn, 0, len(s.clients))
	lockList := make([]*sync.Mutex, 0, len(s.clients))
	for client := range s.clients {
		if lock, exists := s.clientLocks[client]; exists {
			clientList = append(clientList, client)
			lockList = append(lockList, lock)
		}
	}
	s.clientsMutex.RUnlock()

	// 向每个客户端发送消息，使用各自的锁保护写入
	for i, client := range clientList {
		lock := lockList[i]
		go func(c *websocket.Conn, l *sync.Mutex) {
			l.Lock()
			defer l.Unlock()

			err := c.WriteMessage(websocket.TextMessage, statusData)
			if err != nil {
				log.Printf("发送WebSocket消息失败: %v", err)
				// 标记连接需要清理
				s.clientsMutex.Lock()
				if _, exists := s.clients[c]; exists {
					delete(s.clients, c)
					delete(s.clientLocks, c)
					c.Close()
				}
				s.clientsMutex.Unlock()
			}
		}(client, lock)
	}
}

// BroadcastConnectionStatus 广播连接状态给所有连接的客户端
func (s *Server) BroadcastConnectionStatus() {
	agvConnected := s.agvController.IsConnected()
	plcConnected := s.plcController.IsConnected()

	statusData, err := json.Marshal(map[string]interface{}{
		"type": "connection_status",
		"data": map[string]interface{}{
			"agv": map[string]interface{}{
				"connected": agvConnected,
			},
			"plc": map[string]interface{}{
				"connected": plcConnected,
			},
			"allConnected": agvConnected && plcConnected,
		},
	})
	if err != nil {
		log.Printf("编码连接状态数据失败: %v", err)
		return
	}

	s.clientsMutex.RLock()
	// 复制客户端列表和锁引用，避免在持有读锁时进行写操作
	clientList := make([]*websocket.Conn, 0, len(s.clients))
	lockList := make([]*sync.Mutex, 0, len(s.clients))
	for client := range s.clients {
		if lock, exists := s.clientLocks[client]; exists {
			clientList = append(clientList, client)
			lockList = append(lockList, lock)
		}
	}
	s.clientsMutex.RUnlock()

	// 向每个客户端发送消息，使用各自的锁保护写入
	for i, client := range clientList {
		lock := lockList[i]
		go func(c *websocket.Conn, l *sync.Mutex) {
			l.Lock()
			defer l.Unlock()

			err := c.WriteMessage(websocket.TextMessage, statusData)
			if err != nil {
				log.Printf("发送WebSocket消息失败: %v", err)
				// 标记连接需要清理
				s.clientsMutex.Lock()
				if _, exists := s.clients[c]; exists {
					delete(s.clients, c)
					delete(s.clientLocks, c)
					c.Close()
				}
				s.clientsMutex.Unlock()
			}
		}(client, lock)
	}
}

// BroadcastTaskStatusGlobal 全局任务状态广播函数，供任务模块调用
func BroadcastTaskStatusGlobal() {
	if globalServer != nil {
		globalServer.BroadcastTaskStatus()
	}
}

// SetupRoutes 设置路由
func (s *Server) SetupRoutes() *mux.Router {
	r := mux.NewRouter()

	// 静态文件服务（为前端服务）
	r.PathPrefix("/static/").Handler(http.StripPrefix("/static/", http.FileServer(http.Dir("./frontend/dist/"))))

	// API路由
	api := r.PathPrefix("/api").Subrouter()

	// AGV相关API
	agvRouter := api.PathPrefix("/agv").Subrouter()
	agvRouter.HandleFunc("/connect", s.handleAGVConnect).Methods("POST", "OPTIONS")
	agvRouter.HandleFunc("/disconnect", s.handleAGVDisconnect).Methods("POST", "OPTIONS")
	agvRouter.HandleFunc("/status", s.handleAGVStatus).Methods("GET", "OPTIONS")
	agvRouter.HandleFunc("/navigate", s.handleAGVNavigate).Methods("POST", "OPTIONS")
	agvRouter.HandleFunc("/subscribe", s.handleAGVSubscribe).Methods("POST", "OPTIONS")
	agvRouter.HandleFunc("/work-mode", s.handleAGVWorkMode).Methods("POST", "OPTIONS")
	agvRouter.HandleFunc("/position", s.handleAGVPosition).Methods("POST", "OPTIONS")
	agvRouter.HandleFunc("/confirm-position", s.handleAGVConfirmPosition).Methods("POST", "OPTIONS")
	agvRouter.HandleFunc("/laser-init", s.handleAGVLaserInit).Methods("POST", "OPTIONS")
	agvRouter.HandleFunc("/one-click-online", s.handleOneClickOnline).Methods("POST", "OPTIONS")
	agvRouter.HandleFunc("/navigation-status", s.handleAGVNavigationStatus).Methods("GET", "OPTIONS")

	// PLC相关API
	plcRouter := api.PathPrefix("/plc").Subrouter()
	plcRouter.HandleFunc("/connect", s.handlePLCConnect).Methods("POST", "OPTIONS")
	plcRouter.HandleFunc("/disconnect", s.handlePLCDisconnect).Methods("POST", "OPTIONS")
	plcRouter.HandleFunc("/status", s.handlePLCStatus).Methods("GET", "OPTIONS")
	plcRouter.HandleFunc("/read", s.handlePLCRead).Methods("POST", "OPTIONS")
	plcRouter.HandleFunc("/write", s.handlePLCWrite).Methods("POST", "OPTIONS")

	// 任务相关API
	taskRouter := api.PathPrefix("/task").Subrouter()
	taskRouter.HandleFunc("/start-watch", s.StartWatchTaskHandler).Methods("POST", "OPTIONS")
	taskRouter.HandleFunc("/stop-watch", s.StopWatchTaskHandler).Methods("POST", "OPTIONS")
	taskRouter.HandleFunc("/pause-watch", s.PauseWatchTaskHandler).Methods("POST", "OPTIONS")
	taskRouter.HandleFunc("/resume-watch", s.ResumeWatchTaskHandler).Methods("POST", "OPTIONS")
	taskRouter.HandleFunc("/watch-status", s.GetWatchTaskStatusHandler).Methods("GET", "OPTIONS")

	// 配置相关API
	configRouter := api.PathPrefix("/config").Subrouter()
	configRouter.HandleFunc("/defaults", s.handleGetDefaultConfig).Methods("GET", "OPTIONS")
	configRouter.HandleFunc("/test-agv", s.handleTestAGVConnection).Methods("POST", "OPTIONS")
	configRouter.HandleFunc("/test-plc", s.handleTestPLCConnection).Methods("POST", "OPTIONS")
	configRouter.HandleFunc("/connect-all", s.handleConnectAll).Methods("POST", "OPTIONS")
	configRouter.HandleFunc("/disconnect-all", s.handleDisconnectAll).Methods("POST", "OPTIONS")
	configRouter.HandleFunc("/task-config", s.handleGetTaskConfig).Methods("GET", "OPTIONS")
	configRouter.HandleFunc("/task-config", s.handleSaveTaskConfig).Methods("POST", "OPTIONS")
	configRouter.HandleFunc("/task-config/validate", s.handleValidateTaskConfig).Methods("POST", "OPTIONS")
	log.Printf("🔧 配置API路由已注册: /api/config/task-config")

	// 状态相关API
	statusRouter := api.PathPrefix("/status").Subrouter()
	statusRouter.HandleFunc("/connections", s.handleGetConnectionStatus).Methods("GET", "OPTIONS")

	// 监控相关API
	monitorRouter := api.PathPrefix("/monitor").Subrouter()
	monitorRouter.HandleFunc("/flow-status", s.handleGetFlowStatus).Methods("GET", "OPTIONS")

	// 日志相关API
	logRouter := api.PathPrefix("/logs").Subrouter()
	logHandlers := s.AddLogHandlers()
	logRouter.HandleFunc("/query", logHandlers.QueryLogs).Methods("GET", "OPTIONS")
	logRouter.HandleFunc("/stats", logHandlers.GetLogStats).Methods("GET", "OPTIONS")
	logRouter.HandleFunc("/export", logHandlers.ExportLogs).Methods("GET", "OPTIONS")
	logRouter.HandleFunc("/modules", logHandlers.GetLogModules).Methods("GET", "OPTIONS")
	logRouter.HandleFunc("/levels", logHandlers.GetLogLevels).Methods("GET", "OPTIONS")
	logRouter.HandleFunc("/clear-cache", logHandlers.ClearLogCache).Methods("POST", "OPTIONS")

	// 缓存相关API
	cacheRouter := api.PathPrefix("/cache").Subrouter()
	cacheRouter.HandleFunc("/start", s.handleCacheStart).Methods("POST", "OPTIONS")
	cacheRouter.HandleFunc("/stop", s.handleCacheStop).Methods("POST", "OPTIONS")
	cacheRouter.HandleFunc("/status", s.handleCacheStatus).Methods("GET", "OPTIONS")
	cacheRouter.HandleFunc("/data", s.handleCacheData).Methods("GET", "OPTIONS")
	cacheRouter.HandleFunc("/clear", s.handleCacheClear).Methods("POST", "OPTIONS")

	// 自动看车相关API
	autoWatchRouter := api.PathPrefix("/auto-watch").Subrouter()
	autoWatchRouter.HandleFunc("/start", s.handleStartAutoWatch).Methods("POST", "OPTIONS")
	autoWatchRouter.HandleFunc("/stop", s.handleStopAutoWatch).Methods("POST", "OPTIONS")
	autoWatchRouter.HandleFunc("/status", s.handleGetAutoWatchStatus).Methods("GET", "OPTIONS")

	// ZMQ心跳测试相关API
	zmqRouter := api.PathPrefix("/zmq").Subrouter()
	zmqRouter.HandleFunc("/heartbeat-test/start", s.handleStartHeartbeatTest).Methods("POST", "OPTIONS")
	zmqRouter.HandleFunc("/heartbeat-test/stop", s.handleStopHeartbeatTest).Methods("POST", "OPTIONS")
	zmqRouter.HandleFunc("/heartbeat-test/status", s.handleGetHeartbeatTestStatus).Methods("GET", "OPTIONS")

	// PLC测试相关API
	s.plcTestAPI.RegisterRoutes(api)

	// AGV测试相关API
	if s.agvTestAPI != nil {
		s.agvTestAPI.RegisterRoutes(api)
	}

	// WebSocket端点
	r.HandleFunc("/ws", s.handleWebSocket)

	// 首页重定向到前端应用
	r.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		http.ServeFile(w, r, "./frontend/dist/index.html")
	})

	return r
}

// Start 启动服务器
func (s *Server) Start(port string) error {
	router := s.SetupRoutes()

	log.Printf("🚀 API服务器启动在端口 %s", port)
	log.Printf("📡 WebSocket端点: ws://localhost:%s/ws", port)
	log.Printf("🌐 HTTP API: http://localhost:%s/api", port)

	return http.ListenAndServe(":"+port, router)
}

// 添加CORS支持
func (s *Server) enableCORS(w http.ResponseWriter) {
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")
}

// 响应JSON
func (s *Server) respondJSON(w http.ResponseWriter, data interface{}) {
	s.enableCORS(w)
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(data)
}

// 响应错误
func (s *Server) respondError(w http.ResponseWriter, message string, code int) {
	s.enableCORS(w)
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(code)
	json.NewEncoder(w).Encode(map[string]string{"error": message})
}

// 缓存相关处理器

// handleCacheStart 启动缓存服务
func (s *Server) handleCacheStart(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		s.enableCORS(w)
		w.WriteHeader(http.StatusOK)
		return
	}

	var req cache.CacheRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		s.respondError(w, "请求数据格式错误", http.StatusBadRequest)
		return
	}

	// 验证参数
	if len(req.Machines) == 0 {
		s.respondError(w, "请选择要缓存的细纱机", http.StatusBadRequest)
		return
	}

	if req.Interval <= 0 {
		s.respondError(w, "缓存间隔必须大于0", http.StatusBadRequest)
		return
	}

	// 转换间隔时间
	interval := time.Duration(req.Interval) * time.Minute

	// 启动缓存服务
	err := cache.StartCache(req.Machines, interval)
	if err != nil {
		s.respondError(w, err.Error(), http.StatusInternalServerError)
		return
	}

	s.respondJSON(w, cache.CacheResponse{
		Success: true,
		Message: "缓存服务已启动",
		Data: map[string]interface{}{
			"machines": req.Machines,
			"interval": req.Interval,
		},
	})
}

// handleCacheStop 停止缓存服务
func (s *Server) handleCacheStop(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		s.enableCORS(w)
		w.WriteHeader(http.StatusOK)
		return
	}

	err := cache.StopCache()
	if err != nil {
		s.respondError(w, err.Error(), http.StatusInternalServerError)
		return
	}

	s.respondJSON(w, cache.CacheResponse{
		Success: true,
		Message: "缓存服务已停止",
	})
}

// handleCacheStatus 获取缓存状态
func (s *Server) handleCacheStatus(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		s.enableCORS(w)
		w.WriteHeader(http.StatusOK)
		return
	}

	status := cache.GetCacheStatus()

	s.respondJSON(w, cache.CacheResponse{
		Success: true,
		Message: "获取缓存状态成功",
		Data:    status,
	})
}

// handleCacheClear 清空缓存
func (s *Server) handleCacheClear(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		s.enableCORS(w)
		w.WriteHeader(http.StatusOK)
		return
	}

	cache.ClearCache()

	s.respondJSON(w, cache.CacheResponse{
		Success: true,
		Message: "缓存已清空",
	})
}

// handleCacheData 获取缓存数据
func (s *Server) handleCacheData(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		s.enableCORS(w)
		w.WriteHeader(http.StatusOK)
		return
	}

	data, err := cache.GetCacheData()
	if err != nil {
		s.respondJSON(w, cache.CacheResponse{
			Success: false,
			Message: err.Error(),
		})
		return
	}

	s.respondJSON(w, cache.CacheResponse{
		Success: true,
		Message: "获取缓存数据成功",
		Data:    data,
	})
}

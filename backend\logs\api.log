{"timestamp":"2025-06-25 15:31:44.448","level":"INFO","module":"api","caller":"test_logger.go:29","message":"API服务器启动在端口: 8080"}
{"timestamp":"2025-06-25 15:31:44.448","level":"INFO","module":"api","caller":"module_loggers.go:176","message":"收到HTTP请求: GET /api/status"}
{"timestamp":"2025-07-28 17:13:27.042","level":"INFO","module":"api","caller":"websocket_handlers.go:24","message":"WebSocket连接请求","clientIP":"[::1]:60390","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"timestamp":"2025-07-28 17:13:27.043","level":"INFO","module":"api","caller":"websocket_handlers.go:40","message":"新WebSocket客户端连接成功","clientIP":"[::1]:60390","totalClients":"1"}
{"timestamp":"2025-07-28 17:13:27.063","level":"INFO","module":"api","caller":"websocket_handlers.go:24","message":"WebSocket连接请求","clientIP":"[::1]:60391","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"timestamp":"2025-07-28 17:13:27.063","level":"INFO","module":"api","caller":"websocket_handlers.go:40","message":"新WebSocket客户端连接成功","clientIP":"[::1]:60391","totalClients":"2"}
{"timestamp":"2025-07-28 17:13:28.206","level":"INFO","module":"api","caller":"websocket_handlers.go:63","message":"WebSocket客户端断开连接","clientIP":"[::1]:60390","remainingClients":"1"}
{"timestamp":"2025-07-28 17:13:28.210","level":"INFO","module":"api","caller":"config_handlers.go:38","message":"获取默认配置API请求","method":"GET","path":"/api/config/defaults","clientIP":"[::1]:60392"}
{"timestamp":"2025-07-28 17:13:28.210","level":"INFO","module":"api","caller":"config_handlers.go:59","message":"默认配置获取成功","agvIP":"***************","agvPort":"17804","plcIP":"**************","plcPort":"502","clientIP":"[::1]:60392","duration":"0s"}
{"timestamp":"2025-07-28 17:16:18.351","level":"INFO","module":"api","caller":"config_handlers.go:38","message":"获取默认配置API请求","method":"OPTIONS","path":"/api/config/defaults","clientIP":"[::1]:60879"}
{"timestamp":"2025-07-28 17:16:18.352","level":"INFO","module":"api","caller":"config_handlers.go:59","message":"默认配置获取成功","agvIP":"***************","agvPort":"17804","plcIP":"**************","plcPort":"502","clientIP":"[::1]:60879","duration":"527.8µs"}
{"timestamp":"2025-07-28 17:16:18.353","level":"INFO","module":"api","caller":"config_handlers.go:38","message":"获取默认配置API请求","method":"GET","path":"/api/config/defaults","clientIP":"[::1]:60879"}
{"timestamp":"2025-07-28 17:16:18.353","level":"INFO","module":"api","caller":"config_handlers.go:59","message":"默认配置获取成功","agvIP":"***************","agvPort":"17804","plcIP":"**************","plcPort":"502","clientIP":"[::1]:60879","duration":"0s"}
{"timestamp":"2025-07-28 17:16:56.488","level":"INFO","module":"api","caller":"websocket_handlers.go:63","message":"WebSocket客户端断开连接","clientIP":"[::1]:60391","remainingClients":"0"}
{"timestamp":"2025-07-28 17:16:57.171","level":"INFO","module":"api","caller":"websocket_handlers.go:24","message":"WebSocket连接请求","clientIP":"[::1]:60961","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"timestamp":"2025-07-28 17:16:57.171","level":"INFO","module":"api","caller":"websocket_handlers.go:40","message":"新WebSocket客户端连接成功","clientIP":"[::1]:60961","totalClients":"1"}
{"timestamp":"2025-07-28 17:16:57.183","level":"INFO","module":"api","caller":"websocket_handlers.go:24","message":"WebSocket连接请求","clientIP":"[::1]:60962","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"timestamp":"2025-07-28 17:16:57.184","level":"INFO","module":"api","caller":"websocket_handlers.go:40","message":"新WebSocket客户端连接成功","clientIP":"[::1]:60962","totalClients":"2"}
{"timestamp":"2025-07-28 17:17:02.721","level":"INFO","module":"api","caller":"websocket_handlers.go:63","message":"WebSocket客户端断开连接","clientIP":"[::1]:60961","remainingClients":"1"}
{"timestamp":"2025-07-28 17:17:02.723","level":"INFO","module":"api","caller":"config_handlers.go:38","message":"获取默认配置API请求","method":"OPTIONS","path":"/api/config/defaults","clientIP":"[::1]:60879"}
{"timestamp":"2025-07-28 17:17:02.723","level":"INFO","module":"api","caller":"config_handlers.go:59","message":"默认配置获取成功","agvIP":"***************","agvPort":"17804","plcIP":"**************","plcPort":"502","clientIP":"[::1]:60879","duration":"0s"}
{"timestamp":"2025-07-28 17:17:02.724","level":"INFO","module":"api","caller":"config_handlers.go:38","message":"获取默认配置API请求","method":"GET","path":"/api/config/defaults","clientIP":"[::1]:60879"}
{"timestamp":"2025-07-28 17:17:02.724","level":"INFO","module":"api","caller":"config_handlers.go:59","message":"默认配置获取成功","agvIP":"***************","agvPort":"17804","plcIP":"**************","plcPort":"502","clientIP":"[::1]:60879","duration":"0s"}
{"timestamp":"2025-07-28 17:34:07.038","level":"INFO","module":"api","caller":"task_config_handlers.go:82","message":"获取任务配置API请求","method":"GET","path":"/api/config/task-config","clientIP":"[::1]:64761"}
{"timestamp":"2025-07-28 17:34:07.040","level":"INFO","module":"api","caller":"task_config_handlers.go:116","message":"获取任务配置API完成","duration":"1.7057ms","clientIP":"[::1]:64761"}
{"timestamp":"2025-07-28 17:36:14.853","level":"INFO","module":"api","caller":"websocket_handlers.go:24","message":"WebSocket连接请求","clientIP":"[::1]:65032","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"timestamp":"2025-07-28 17:36:14.853","level":"INFO","module":"api","caller":"websocket_handlers.go:40","message":"新WebSocket客户端连接成功","clientIP":"[::1]:65032","totalClients":"1"}
{"timestamp":"2025-07-28 17:36:14.875","level":"INFO","module":"api","caller":"websocket_handlers.go:24","message":"WebSocket连接请求","clientIP":"[::1]:65033","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"timestamp":"2025-07-28 17:36:14.875","level":"INFO","module":"api","caller":"websocket_handlers.go:40","message":"新WebSocket客户端连接成功","clientIP":"[::1]:65033","totalClients":"2"}
{"timestamp":"2025-07-28 17:36:20.689","level":"INFO","module":"api","caller":"websocket_handlers.go:63","message":"WebSocket客户端断开连接","clientIP":"[::1]:65032","remainingClients":"1"}
{"timestamp":"2025-07-28 17:36:20.691","level":"INFO","module":"api","caller":"config_handlers.go:38","message":"获取默认配置API请求","method":"OPTIONS","path":"/api/config/defaults","clientIP":"[::1]:65030"}
{"timestamp":"2025-07-28 17:36:20.691","level":"INFO","module":"api","caller":"config_handlers.go:59","message":"默认配置获取成功","agvIP":"***************","agvPort":"17804","plcIP":"**************","plcPort":"502","clientIP":"[::1]:65030","duration":"371.4µs"}
{"timestamp":"2025-07-28 17:36:20.692","level":"INFO","module":"api","caller":"config_handlers.go:38","message":"获取默认配置API请求","method":"GET","path":"/api/config/defaults","clientIP":"[::1]:65030"}
{"timestamp":"2025-07-28 17:36:20.692","level":"INFO","module":"api","caller":"config_handlers.go:59","message":"默认配置获取成功","agvIP":"***************","agvPort":"17804","plcIP":"**************","plcPort":"502","clientIP":"[::1]:65030","duration":"0s"}
{"timestamp":"2025-07-28 17:36:27.191","level":"INFO","module":"api","caller":"task_config_handlers.go:82","message":"获取任务配置API请求","method":"GET","path":"/api/config/task-config","clientIP":"[::1]:65030"}
{"timestamp":"2025-07-28 17:36:27.192","level":"INFO","module":"api","caller":"task_config_handlers.go:116","message":"获取任务配置API完成","duration":"1.0511ms","clientIP":"[::1]:65030"}
{"timestamp":"2025-07-28 17:36:29.704","level":"INFO","module":"api","caller":"websocket_handlers.go:63","message":"WebSocket客户端断开连接","clientIP":"[::1]:65033","remainingClients":"0"}
{"timestamp":"2025-07-28 17:36:30.372","level":"INFO","module":"api","caller":"websocket_handlers.go:24","message":"WebSocket连接请求","clientIP":"[::1]:65052","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"timestamp":"2025-07-28 17:36:30.373","level":"INFO","module":"api","caller":"websocket_handlers.go:40","message":"新WebSocket客户端连接成功","clientIP":"[::1]:65052","totalClients":"1"}
{"timestamp":"2025-07-28 17:36:30.395","level":"INFO","module":"api","caller":"websocket_handlers.go:24","message":"WebSocket连接请求","clientIP":"[::1]:65053","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"timestamp":"2025-07-28 17:36:30.396","level":"INFO","module":"api","caller":"websocket_handlers.go:40","message":"新WebSocket客户端连接成功","clientIP":"[::1]:65053","totalClients":"2"}
{"timestamp":"2025-07-28 17:36:33.526","level":"INFO","module":"api","caller":"websocket_handlers.go:63","message":"WebSocket客户端断开连接","clientIP":"[::1]:65052","remainingClients":"1"}
{"timestamp":"2025-07-28 17:36:39.380","level":"INFO","module":"api","caller":"config_handlers.go:38","message":"获取默认配置API请求","method":"OPTIONS","path":"/api/config/defaults","clientIP":"[::1]:65030"}
{"timestamp":"2025-07-28 17:36:39.380","level":"INFO","module":"api","caller":"config_handlers.go:59","message":"默认配置获取成功","agvIP":"***************","agvPort":"17804","plcIP":"**************","plcPort":"502","clientIP":"[::1]:65030","duration":"558.2µs"}
{"timestamp":"2025-07-28 17:36:39.382","level":"INFO","module":"api","caller":"config_handlers.go:38","message":"获取默认配置API请求","method":"GET","path":"/api/config/defaults","clientIP":"[::1]:65030"}
{"timestamp":"2025-07-28 17:36:39.382","level":"INFO","module":"api","caller":"config_handlers.go:59","message":"默认配置获取成功","agvIP":"***************","agvPort":"17804","plcIP":"**************","plcPort":"502","clientIP":"[::1]:65030","duration":"0s"}
{"timestamp":"2025-07-28 17:36:41.189","level":"INFO","module":"api","caller":"websocket_handlers.go:24","message":"WebSocket连接请求","clientIP":"[::1]:65086","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"timestamp":"2025-07-28 17:36:41.190","level":"INFO","module":"api","caller":"websocket_handlers.go:40","message":"新WebSocket客户端连接成功","clientIP":"[::1]:65086","totalClients":"2"}
{"timestamp":"2025-07-28 17:36:46.013","level":"INFO","module":"api","caller":"config_handlers.go:38","message":"获取默认配置API请求","method":"OPTIONS","path":"/api/config/defaults","clientIP":"[::1]:65077"}
{"timestamp":"2025-07-28 17:36:46.013","level":"INFO","module":"api","caller":"config_handlers.go:59","message":"默认配置获取成功","agvIP":"***************","agvPort":"17804","plcIP":"**************","plcPort":"502","clientIP":"[::1]:65077","duration":"0s"}
{"timestamp":"2025-07-28 17:36:46.013","level":"INFO","module":"api","caller":"websocket_handlers.go:63","message":"WebSocket客户端断开连接","clientIP":"[::1]:65086","remainingClients":"1"}
{"timestamp":"2025-07-28 17:36:46.015","level":"INFO","module":"api","caller":"config_handlers.go:38","message":"获取默认配置API请求","method":"GET","path":"/api/config/defaults","clientIP":"[::1]:65077"}
{"timestamp":"2025-07-28 17:36:46.015","level":"INFO","module":"api","caller":"config_handlers.go:59","message":"默认配置获取成功","agvIP":"***************","agvPort":"17804","plcIP":"**************","plcPort":"502","clientIP":"[::1]:65077","duration":"0s"}
{"timestamp":"2025-07-28 17:36:49.714","level":"INFO","module":"api","caller":"task_config_handlers.go:82","message":"获取任务配置API请求","method":"GET","path":"/api/config/task-config","clientIP":"[::1]:65077"}
{"timestamp":"2025-07-28 17:36:49.715","level":"INFO","module":"api","caller":"task_config_handlers.go:116","message":"获取任务配置API完成","duration":"628.2µs","clientIP":"[::1]:65077"}
{"timestamp":"2025-07-28 17:41:34.980","level":"INFO","module":"api","caller":"task_config_handlers.go:82","message":"获取任务配置API请求","method":"GET","path":"/api/config/task-config","clientIP":"[::1]:49848"}
{"timestamp":"2025-07-28 17:41:34.980","level":"INFO","module":"api","caller":"task_config_handlers.go:116","message":"获取任务配置API完成","duration":"1.1548ms","clientIP":"[::1]:49848"}
{"timestamp":"2025-07-28 17:41:47.875","level":"INFO","module":"api","caller":"task_config_handlers.go:82","message":"获取任务配置API请求","method":"GET","path":"/api/config/task-config","clientIP":"[::1]:49924"}
{"timestamp":"2025-07-28 17:41:47.875","level":"INFO","module":"api","caller":"task_config_handlers.go:116","message":"获取任务配置API完成","duration":"0s","clientIP":"[::1]:49924"}
{"timestamp":"2025-07-28 17:48:39.851","level":"INFO","module":"api","caller":"websocket_handlers.go:24","message":"WebSocket连接请求","clientIP":"[::1]:51380","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"timestamp":"2025-07-28 17:48:39.851","level":"INFO","module":"api","caller":"websocket_handlers.go:40","message":"新WebSocket客户端连接成功","clientIP":"[::1]:51380","totalClients":"1"}
{"timestamp":"2025-07-28 17:58:35.686","level":"INFO","module":"api","caller":"websocket_handlers.go:63","message":"WebSocket客户端断开连接","clientIP":"[::1]:51380","remainingClients":"0"}
{"timestamp":"2025-07-28 17:58:36.362","level":"INFO","module":"api","caller":"websocket_handlers.go:24","message":"WebSocket连接请求","clientIP":"[::1]:52390","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"timestamp":"2025-07-28 17:58:36.362","level":"INFO","module":"api","caller":"websocket_handlers.go:40","message":"新WebSocket客户端连接成功","clientIP":"[::1]:52390","totalClients":"1"}
{"timestamp":"2025-07-28 17:58:36.378","level":"INFO","module":"api","caller":"websocket_handlers.go:24","message":"WebSocket连接请求","clientIP":"[::1]:52391","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"timestamp":"2025-07-28 17:58:36.378","level":"INFO","module":"api","caller":"websocket_handlers.go:40","message":"新WebSocket客户端连接成功","clientIP":"[::1]:52391","totalClients":"2"}
{"timestamp":"2025-07-28 17:58:39.465","level":"INFO","module":"api","caller":"websocket_handlers.go:63","message":"WebSocket客户端断开连接","clientIP":"[::1]:52390","remainingClients":"1"}
{"timestamp":"2025-07-28 17:58:39.976","level":"INFO","module":"api","caller":"websocket_handlers.go:24","message":"WebSocket连接请求","clientIP":"[::1]:52415","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"timestamp":"2025-07-28 17:58:39.976","level":"INFO","module":"api","caller":"websocket_handlers.go:40","message":"新WebSocket客户端连接成功","clientIP":"[::1]:52415","totalClients":"2"}
{"timestamp":"2025-07-28 17:58:40.751","level":"INFO","module":"api","caller":"websocket_handlers.go:63","message":"WebSocket客户端断开连接","clientIP":"[::1]:52415","remainingClients":"1"}
{"timestamp":"2025-07-28 17:58:40.754","level":"INFO","module":"api","caller":"config_handlers.go:38","message":"获取默认配置API请求","method":"OPTIONS","path":"/api/config/defaults","clientIP":"[::1]:52388"}
{"timestamp":"2025-07-28 17:58:40.754","level":"INFO","module":"api","caller":"config_handlers.go:59","message":"默认配置获取成功","agvIP":"***************","agvPort":"17804","plcIP":"**************","plcPort":"502","clientIP":"[::1]:52388","duration":"0s"}
{"timestamp":"2025-07-28 17:58:40.755","level":"INFO","module":"api","caller":"config_handlers.go:38","message":"获取默认配置API请求","method":"GET","path":"/api/config/defaults","clientIP":"[::1]:52388"}
{"timestamp":"2025-07-28 17:58:40.756","level":"INFO","module":"api","caller":"config_handlers.go:59","message":"默认配置获取成功","agvIP":"***************","agvPort":"17804","plcIP":"**************","plcPort":"502","clientIP":"[::1]:52388","duration":"406µs"}
{"timestamp":"2025-07-28 17:58:45.705","level":"INFO","module":"api","caller":"task_config_handlers.go:82","message":"获取任务配置API请求","method":"GET","path":"/api/config/task-config","clientIP":"[::1]:52388"}
{"timestamp":"2025-07-28 17:58:45.707","level":"INFO","module":"api","caller":"task_config_handlers.go:116","message":"获取任务配置API完成","duration":"1.2246ms","clientIP":"[::1]:52388"}
{"timestamp":"2025-07-30 11:26:30.674","level":"INFO","module":"api","caller":"websocket_handlers.go:24","message":"WebSocket连接请求","clientIP":"[::1]:62443","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) frontend/1.0.0 Chrome/136.0.7103.168 Electron/36.5.0 Safari/537.36"}
{"timestamp":"2025-07-30 11:26:30.676","level":"INFO","module":"api","caller":"websocket_handlers.go:40","message":"新WebSocket客户端连接成功","clientIP":"[::1]:62443","totalClients":"1"}
{"timestamp":"2025-07-30 11:26:30.698","level":"INFO","module":"api","caller":"websocket_handlers.go:24","message":"WebSocket连接请求","clientIP":"[::1]:62444","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) frontend/1.0.0 Chrome/136.0.7103.168 Electron/36.5.0 Safari/537.36"}
{"timestamp":"2025-07-30 11:26:30.699","level":"INFO","module":"api","caller":"websocket_handlers.go:40","message":"新WebSocket客户端连接成功","clientIP":"[::1]:62444","totalClients":"2"}
{"timestamp":"2025-07-30 11:26:39.891","level":"INFO","module":"api","caller":"websocket_handlers.go:63","message":"WebSocket客户端断开连接","clientIP":"[::1]:62443","remainingClients":"1"}
{"timestamp":"2025-07-30 11:26:57.975","level":"INFO","module":"api","caller":"zmq_handlers.go:61","message":"启动ZMQ心跳测试API请求","method":"POST","path":"/api/zmq/heartbeat-test/start","clientIP":"[::1]:62496"}
{"timestamp":"2025-07-30 11:26:57.984","level":"INFO","module":"api","caller":"zmq_handlers.go:78","message":"ZMQ心跳测试启动成功","clientIP":"[::1]:62496","duration":"8.7277ms"}
{"timestamp":"2025-07-30 11:27:39.746","level":"INFO","module":"api","caller":"websocket_handlers.go:63","message":"WebSocket客户端断开连接","clientIP":"[::1]:62444","remainingClients":"0"}
{"timestamp":"2025-07-30 11:32:04.867","level":"INFO","module":"api","caller":"websocket_handlers.go:24","message":"WebSocket连接请求","clientIP":"[::1]:62986","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) frontend/1.0.0 Chrome/136.0.7103.168 Electron/36.5.0 Safari/537.36"}
{"timestamp":"2025-07-30 11:32:04.868","level":"INFO","module":"api","caller":"websocket_handlers.go:40","message":"新WebSocket客户端连接成功","clientIP":"[::1]:62986","totalClients":"1"}
{"timestamp":"2025-07-30 11:32:04.880","level":"INFO","module":"api","caller":"websocket_handlers.go:24","message":"WebSocket连接请求","clientIP":"[::1]:62987","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) frontend/1.0.0 Chrome/136.0.7103.168 Electron/36.5.0 Safari/537.36"}
{"timestamp":"2025-07-30 11:32:04.881","level":"INFO","module":"api","caller":"websocket_handlers.go:40","message":"新WebSocket客户端连接成功","clientIP":"[::1]:62987","totalClients":"2"}
{"timestamp":"2025-07-30 11:32:13.657","level":"INFO","module":"api","caller":"websocket_handlers.go:63","message":"WebSocket客户端断开连接","clientIP":"[::1]:62986","remainingClients":"1"}
{"timestamp":"2025-07-30 11:32:17.610","level":"INFO","module":"api","caller":"zmq_handlers.go:100","message":"停止ZMQ心跳测试API请求","method":"POST","path":"/api/zmq/heartbeat-test/stop","clientIP":"[::1]:63012"}
{"timestamp":"2025-07-30 11:32:17.610","level":"INFO","module":"api","caller":"zmq_handlers.go:117","message":"ZMQ心跳测试停止成功","clientIP":"[::1]:63012","duration":"582µs"}
{"timestamp":"2025-07-30 11:32:23.828","level":"INFO","module":"api","caller":"zmq_handlers.go:61","message":"启动ZMQ心跳测试API请求","method":"POST","path":"/api/zmq/heartbeat-test/start","clientIP":"[::1]:63045"}
{"timestamp":"2025-07-30 11:32:23.829","level":"INFO","module":"api","caller":"zmq_handlers.go:78","message":"ZMQ心跳测试启动成功","clientIP":"[::1]:63045","duration":"579.4µs"}
{"timestamp":"2025-07-30 11:32:34.667","level":"INFO","module":"api","caller":"zmq_handlers.go:100","message":"停止ZMQ心跳测试API请求","method":"POST","path":"/api/zmq/heartbeat-test/stop","clientIP":"[::1]:63069"}
{"timestamp":"2025-07-30 11:32:34.668","level":"INFO","module":"api","caller":"zmq_handlers.go:117","message":"ZMQ心跳测试停止成功","clientIP":"[::1]:63069","duration":"1.1179ms"}
{"timestamp":"2025-07-30 11:34:02.407","level":"INFO","module":"api","caller":"websocket_handlers.go:24","message":"WebSocket连接请求","clientIP":"[::1]:63293","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) frontend/1.0.0 Chrome/136.0.7103.168 Electron/36.5.0 Safari/537.36"}
{"timestamp":"2025-07-30 11:34:02.408","level":"INFO","module":"api","caller":"websocket_handlers.go:40","message":"新WebSocket客户端连接成功","clientIP":"[::1]:63293","totalClients":"2"}
{"timestamp":"2025-07-30 11:35:38.483","level":"INFO","module":"api","caller":"websocket_handlers.go:63","message":"WebSocket客户端断开连接","clientIP":"[::1]:63293","remainingClients":"1"}
{"timestamp":"2025-07-30 11:35:38.483","level":"INFO","module":"api","caller":"websocket_handlers.go:63","message":"WebSocket客户端断开连接","clientIP":"[::1]:62987","remainingClients":"0"}

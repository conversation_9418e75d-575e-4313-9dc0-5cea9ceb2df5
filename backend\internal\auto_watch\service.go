package auto_watch

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/user/agv_nav/internal/zmq"
	"github.com/user/agv_nav/pkg/agv"
	"github.com/user/agv_nav/pkg/plc"
	zmqpkg "github.com/user/agv_nav/pkg/zmq"
)

// 全局自动看车服务实例（用于支持全局API接口）
var globalAutoWatchService *AutoWatchService

// AutoWatchTaskStatus 自动看车任务状态（复制自task_types.go）
type AutoWatchTaskStatus string

const (
	AutoWatchStatusIdle    AutoWatchTaskStatus = "idle"    // 空闲
	AutoWatchStatusRunning AutoWatchTaskStatus = "running" // 运行中
	AutoWatchStatusPaused  AutoWatchTaskStatus = "paused"  // 已暂停
	AutoWatchStatusStopped AutoWatchTaskStatus = "stopped" // 已停止
)

// AGVLocation AGV位置状态
type AGVLocation int

const (
	LocationUnknown      AGVLocation = iota // 位置未知
	LocationParkingPoint                    // 停车点
	LocationSwitchPoint                     // 切换点
)

// 前向声明已移除 - 使用实际的类型定义

// ServiceStatus 服务状态（增强版，参考单机看车WatchTaskInfo）
type ServiceStatus struct {
	// 基础服务状态
	IsRunning        bool   `json:"isRunning"`
	IsConnected      bool   `json:"isConnected"`
	RobotNo          string `json:"robotNo"`
	ConnectionStatus string `json:"connectionStatus"` // disconnected, connecting, connected, error, timeout
	LastHeartbeat    string `json:"lastHeartbeat"`

	// 任务执行状态（从WatchTaskInfo复制）
	TaskStatus     AutoWatchTaskStatus      `json:"taskStatus"`     // 任务状态
	MachineList    []string                 `json:"machineList"`    // 机器列表
	CurrentMachine string                   `json:"currentMachine"` // 当前处理的机器
	ProcessedCount int                      `json:"processedCount"` // 已处理数量
	TotalCount     int                      `json:"totalCount"`     // 总数量
	StartTime      *time.Time               `json:"startTime"`      // 开始时间
	LastUpdateTime *time.Time               `json:"lastUpdateTime"` // 最后更新时间
	ErrorMessage   string                   `json:"errorMessage"`   // 错误信息
	ProgressDetail *AutoWatchProgressDetail `json:"progressDetail"` // 详细进度信息

	// 统计信息
	Statistics *Stats `json:"statistics"`

	// 历史信息
	FailedMachines []string    `json:"failedMachines"` // 失败的机器列表
	CompletedTimes []time.Time `json:"completedTimes"` // 完成时间列表
}

// AutoWatchProgressDetail 详细进度信息（从ProgressDetail复制）
type AutoWatchProgressDetail struct {
	CurrentStep     string     `json:"currentStep"`     // 当前步骤
	StepProgress    int        `json:"stepProgress"`    // 步骤进度(百分比)
	TotalSteps      int        `json:"totalSteps"`      // 总步骤数
	CompletedSteps  int        `json:"completedSteps"`  // 已完成步骤数
	EstimatedTime   *time.Time `json:"estimatedTime"`   // 预计完成时间
	ProcessingSpeed float64    `json:"processingSpeed"` // 处理速度(台/分钟)
}

// Task 任务信息
type Task struct {
	TaskId    string `json:"taskId"`
	MachineNo string `json:"machineNo"`
	TaskType  string `json:"taskType"`
	Status    string `json:"status"`
	Progress  int    `json:"progress"`
}

// Stats 统计信息
type Stats struct {
	TotalTasks     int `json:"totalTasks"`
	CompletedTasks int `json:"completedTasks"`
	FailedTasks    int `json:"failedTasks"`
	TotalRunTime   int `json:"totalRunTime"` // 秒
}

// AutoWatchService 自动看车服务
type AutoWatchService struct {
	// ZMQ通信组件
	requester    zmqpkg.Requester
	responder    zmqpkg.Responder
	heartbeatMgr *zmq.HeartbeatManager

	// 配置和状态
	systemConfig     *zmq.SystemConfig
	zmqConfig        *zmqpkg.Config
	isRunning        bool
	isConnected      bool
	connectionStatus string

	// 消息路由
	messageRouter      *MessageRouter
	taskMessageHandler *TaskMessageHandler
	taskReporter       *TaskReporter
	schedulerComm      *ZMQSchedulerCommunicator // ZMQ调度系统通信管理器
	currentTask        *Task

	// 统计信息
	statistics *Stats
	startTime  time.Time

	// 并发控制
	mu     sync.RWMutex
	ctx    context.Context
	cancel context.CancelFunc

	// 任务执行状态（从WatchTaskManager复制）
	queue          []string            // 待处理的机器队列
	currentMachine string              // 当前正在处理的机器
	status         AutoWatchTaskStatus // 任务状态
	progress       int                 // 已完成数量
	total          int                 // 总数量
	stopChan       chan struct{}       // 停止信号
	pauseChan      chan struct{}       // 暂停信号
	resumeChan     chan struct{}       // 恢复信号
	isWorking      bool                // 是否有任务在运行

	// 错误处理配置（从WatchTaskManager复制）
	maxRetries     int      // 最大重试次数
	failedMachines []string // 失败的机器列表
	lastError      string   // 最后的错误信息

	// 详细进度跟踪（从WatchTaskManager复制）
	taskStartTime  *time.Time  // 任务开始时间
	currentStep    string      // 当前步骤描述
	stepProgress   int         // 当前步骤进度
	completedTimes []time.Time // 每台机器完成时间（用于计算速度）

	// AGV位置状态管理
	agvLocation AGVLocation // 当前AGV位置状态

	// 核心业务组件（从WatchTaskManager复制）
	agvController *agv.Controller // AGV控制器
	plcController *plc.Controller // PLC控制器
	dbService     interface{}     // 数据库服务（暂用interface{}，后续补充具体类型）

	// 业务处理器组件
	taskExecutor      *AutoWatchTaskExecutor           // 任务执行器
	spindleService    AutoWatchSpindleServiceInterface // 锭子服务（使用接口）
	navigationHandler *AutoWatchNavigationHandler      // 导航处理器
	plcHandler        *AutoWatchPLCHandler             // PLC处理器
	laneProcessor     *AutoWatchLaneProcessor          // 巷道处理器
	workExecutor      *AutoWatchWorkExecutor           // 工作执行器
	configManager     *AutoWatchConfigManager          // 配置管理器
	// 这些组件暂时移除，将来可以实现
	// retryManager       *AutoWatchRetryManager    // 重试管理器
	// errorHandler       *AutoWatchErrorHandler    // 错误处理器
	// statusReporter     *AutoWatchStatusReporter  // 状态报告器

	// 日志
	logger Logger
}

// Logger 日志接口
type Logger interface {
	Debug(msg string, fields ...interface{})
	Info(msg string, fields ...interface{})
	Warn(msg string, fields ...interface{})
	Error(msg string, fields ...interface{})
}

// defaultLogger 默认日志实现
type defaultLogger struct{}

func (l *defaultLogger) Debug(msg string, fields ...interface{}) {
	fmt.Printf("[DEBUG] AutoWatch: %s %v\n", msg, fields)
}

func (l *defaultLogger) Info(msg string, fields ...interface{}) {
	fmt.Printf("[INFO] AutoWatch: %s %v\n", msg, fields)
}

func (l *defaultLogger) Warn(msg string, fields ...interface{}) {
	fmt.Printf("[WARN] AutoWatch: %s %v\n", msg, fields)
}

func (l *defaultLogger) Error(msg string, fields ...interface{}) {
	fmt.Printf("[ERROR] AutoWatch: %s %v\n", msg, fields)
}

// NewAutoWatchService 创建自动看车服务
func NewAutoWatchService(agvController *agv.Controller, plcController *plc.Controller) (*AutoWatchService, error) {
	// 加载系统配置
	systemConfig, err := zmq.LoadSystemConfig("backend/data/task_config.json")
	if err != nil {
		systemConfig = zmq.DefaultSystemConfig()
	}

	// 创建ZMQ配置
	zmqConfig := zmqpkg.DefaultConfig()
	zmqConfig.RequesterEndpoint = "tcp://192.168.1.103:5556" // 连接到调度系统
	zmqConfig.ResponderEndpoint = "tcp://*:5557"             // 监听调度系统的消息

	service := &AutoWatchService{
		// ZMQ通信配置
		systemConfig:     systemConfig,
		zmqConfig:        zmqConfig,
		isRunning:        false,
		isConnected:      false,
		connectionStatus: "disconnected",

		// 统计信息
		statistics: &Stats{
			TotalTasks:     0,
			CompletedTasks: 0,
			FailedTasks:    0,
			TotalRunTime:   0,
		},

		// 任务执行状态（从WatchTaskManager复制）
		queue:          make([]string, 0),
		currentMachine: "",
		status:         AutoWatchStatusIdle,
		progress:       0,
		total:          0,
		stopChan:       make(chan struct{}),
		pauseChan:      make(chan struct{}),
		resumeChan:     make(chan struct{}),
		isWorking:      false,

		// 错误处理配置（从WatchTaskManager复制）
		maxRetries:     3, // 默认最大重试次数
		failedMachines: make([]string, 0),
		lastError:      "",

		// 详细进度跟踪（从WatchTaskManager复制）
		taskStartTime:  nil,
		currentStep:    "",
		stepProgress:   0,
		completedTimes: make([]time.Time, 0),

		// AGV位置状态管理
		agvLocation: LocationUnknown, // 初始位置未知

		// 核心业务组件（从WatchTaskManager复制）
		agvController: agvController,
		plcController: plcController,
		dbService:     nil, // 后续补充数据库服务

		// 日志
		logger: &defaultLogger{},
	}

	// 创建配置管理器
	service.configManager = NewAutoWatchConfigManager(service.logger)

	// 初始化所有业务组件
	components, err := service.configManager.InitializeComponents(agvController, plcController)
	if err != nil {
		return nil, fmt.Errorf("initialize components failed: %w", err)
	}

	// 设置业务组件
	service.spindleService = components.SpindleService
	service.navigationHandler = components.NavigationHandler
	service.plcHandler = components.PLCHandler
	service.laneProcessor = components.LaneProcessor
	service.workExecutor = components.WorkExecutor

	// 为工作执行器设置服务实例（用于任务完成通知）
	if service.workExecutor != nil {
		service.workExecutor.SetService(service)
	}

	// 为巷道处理器设置服务实例（用于任务完成通知）
	if service.laneProcessor != nil {
		service.laneProcessor.SetService(service)
	}
	service.dbService = components.DBService

	// 重新创建TaskExecutor并传入service实例（用于调度通信）
	service.taskExecutor = NewAutoWatchTaskExecutor(
		service.workExecutor,
		service.laneProcessor,
		service,
		service.logger,
	)

	// 其他组件暂时设为nil，后续可以扩展
	// service.retryManager = nil
	// service.errorHandler = nil
	// service.statusReporter = nil

	// 创建消息路由器
	service.messageRouter = NewMessageRouter(service.logger)

	// 创建任务消息处理器
	service.taskMessageHandler = NewTaskMessageHandler(systemConfig, service.logger)
	service.taskMessageHandler.SetService(service)

	// 注册任务消息处理器到路由器
	service.messageRouter.RegisterHandler(service.taskMessageHandler)

	// 创建任务报告器
	service.taskReporter = NewTaskReporter(service, service.logger)

	// 设置全局服务实例
	globalAutoWatchService = service

	service.logger.Info("AutoWatchService created successfully with all components initialized")
	return service, nil
}

// NewAutoWatchServiceWithLogger 创建带自定义日志的自动看车服务
func NewAutoWatchServiceWithLogger(agvController *agv.Controller, plcController *plc.Controller, logger Logger) (*AutoWatchService, error) {
	service, err := NewAutoWatchService(agvController, plcController)
	if err != nil {
		return nil, err
	}
	service.logger = logger
	return service, nil
}

// Start 启动自动看车服务
func (s *AutoWatchService) Start() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.isRunning {
		return fmt.Errorf("service is already running")
	}

	s.logger.Info("Starting auto watch service", "robotNo", s.systemConfig.RobotNo)

	// 步骤1: 初始化配置和数据库连接（复制自InitTaskManager）
	if err := s.initializeComponents(); err != nil {
		return fmt.Errorf("initialize components failed: %w", err)
	}

	// 步骤2: 前置检查：机器人运行条件检查（复制自StartTask）
	if err := s.checkAutoWatchRunCondition(); err != nil {
		return fmt.Errorf("auto watch run condition check failed: %w", err)
	}

	// 步骤3: 创建上下文
	s.ctx, s.cancel = context.WithCancel(context.Background())

	// 步骤4: 创建ZMQ连接
	if err := s.createZMQConnections(); err != nil {
		return fmt.Errorf("create ZMQ connections failed: %w", err)
	}

	// 步骤5: 启动心跳管理器
	if err := s.startHeartbeat(); err != nil {
		s.cleanup()
		return fmt.Errorf("start heartbeat failed: %w", err)
	}

	// 步骤6: 设置任务消息处理器
	if err := s.setupTaskHandler(); err != nil {
		s.cleanup()
		return fmt.Errorf("setup task handler failed: %w", err)
	}

	// 步骤7: 启动响应者监听消息
	if err := s.startResponder(); err != nil {
		s.cleanup()
		return fmt.Errorf("start responder failed: %w", err)
	}

	// 步骤8: 初始化任务状态（复制自StartTask的初始化部分）
	s.initializeAutoWatchTaskState()

	// 步骤9: 启动状态上报器
	if err := s.startStatusReporter(); err != nil {
		s.cleanup()
		return fmt.Errorf("start status reporter failed: %w", err)
	}

	s.isRunning = true
	s.connectionStatus = "connecting"
	s.startTime = time.Now()

	s.logger.Info("Auto watch service started successfully")

	return nil
}

// Stop 停止自动看车服务
func (s *AutoWatchService) Stop() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !s.isRunning {
		return fmt.Errorf("service is not running")
	}

	s.logger.Info("Stopping auto watch service")

	// 步骤1: 停止正在进行的任务（复制自StopWatchTask逻辑）
	if s.isWorking {
		s.logger.Info("Stopping current auto watch task")
		if err := s.stopAutoWatchTask(); err != nil {
			s.logger.Error("Stop auto watch task failed", "error", err)
			// 继续执行服务停止，不因任务停止失败而中断
		}
	}

	// 步骤2: 停止状态上报器
	if s.schedulerComm != nil {
		s.logger.Info("Stopping status reporter")
		s.schedulerComm.StopStatusReporter()
	}

	// 步骤3: 取消上下文，停止所有goroutine
	if s.cancel != nil {
		s.cancel()
	}

	// 步骤4: 清理ZMQ连接资源
	s.cleanup()

	// 步骤5: 重置服务状态
	s.isRunning = false
	s.isConnected = false
	s.connectionStatus = "disconnected"

	// 步骤6: 重置任务状态（复制自StopWatchTask的状态重置）
	s.resetAutoWatchTaskState()

	s.logger.Info("Auto watch service stopped")

	return nil
}

// GetStatus 获取服务状态（增强版，提供完整状态信息）
func (s *AutoWatchService) GetStatus() *ServiceStatus {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// 获取心跳信息
	var lastHeartbeat string
	if s.heartbeatMgr != nil {
		lastTime := s.heartbeatMgr.GetLastHeartbeat()
		if !lastTime.IsZero() {
			lastHeartbeat = lastTime.Format("15:04:05")
		}
	}

	// 更新运行时间
	if s.isRunning && !s.startTime.IsZero() {
		s.statistics.TotalRunTime = int(time.Since(s.startTime).Seconds())
	}

	// 计算处理速度
	processingSpeed := s.calculateProcessingSpeed()

	// 估算剩余时间
	estimatedTime := s.calculateEstimatedTime(processingSpeed)

	// 获取当前时间作为更新时间
	now := time.Now()

	return &ServiceStatus{
		// 基础服务状态
		IsRunning:        s.isRunning,
		IsConnected:      s.isConnected,
		RobotNo:          s.systemConfig.RobotNo,
		ConnectionStatus: s.connectionStatus,
		LastHeartbeat:    lastHeartbeat,

		// 任务执行状态
		TaskStatus:     s.status,
		MachineList:    s.queue,
		CurrentMachine: s.currentMachine,
		ProcessedCount: s.progress,
		TotalCount:     s.total,
		StartTime:      s.taskStartTime,
		LastUpdateTime: &now,
		ErrorMessage:   s.lastError,
		ProgressDetail: &AutoWatchProgressDetail{
			CurrentStep:     s.currentStep,
			StepProgress:    s.stepProgress,
			TotalSteps:      s.total,
			CompletedSteps:  s.progress,
			EstimatedTime:   estimatedTime,
			ProcessingSpeed: processingSpeed,
		},

		// 统计信息
		Statistics: s.statistics,

		// 历史信息
		FailedMachines: s.failedMachines,
		CompletedTimes: s.completedTimes,
	}
}

// createZMQConnections 创建ZMQ连接（带重试机制）
func (s *AutoWatchService) createZMQConnections() error {
	s.logger.Info("Creating ZMQ connections", "requesterEndpoint", s.zmqConfig.RequesterEndpoint, "responderEndpoint", s.zmqConfig.ResponderEndpoint)

	// 配置重试参数
	maxRetries := 3
	retryInterval := 2 * time.Second

	// 重试创建请求者连接
	var err error
	for attempt := 1; attempt <= maxRetries; attempt++ {
		s.logger.Info("Creating requester connection", "attempt", attempt, "maxRetries", maxRetries)

		s.requester, err = zmqpkg.NewRequester(s.zmqConfig)
		if err == nil {
			s.logger.Info("Requester connection created successfully")
			break
		}

		s.logger.Warn("Create requester failed", "attempt", attempt, "error", err)
		if attempt < maxRetries {
			time.Sleep(retryInterval)
		}
	}

	if err != nil {
		return fmt.Errorf("create requester failed after %d attempts: %w", maxRetries, err)
	}

	// 重试创建响应者连接
	for attempt := 1; attempt <= maxRetries; attempt++ {
		s.logger.Info("Creating responder connection", "attempt", attempt, "maxRetries", maxRetries)

		s.responder, err = zmqpkg.NewResponder(s.zmqConfig)
		if err == nil {
			s.logger.Info("Responder connection created successfully")
			break
		}

		s.logger.Warn("Create responder failed", "attempt", attempt, "error", err)
		if attempt < maxRetries {
			time.Sleep(retryInterval)
		}
	}

	if err != nil {
		// 清理已创建的requester
		if s.requester != nil {
			s.requester.Close()
			s.requester = nil
		}
		return fmt.Errorf("create responder failed after %d attempts: %w", maxRetries, err)
	}

	s.logger.Info("ZMQ connections created successfully", "requester", s.zmqConfig.RequesterEndpoint, "responder", s.zmqConfig.ResponderEndpoint)

	// 创建ZMQ调度系统通信管理器
	s.schedulerComm = NewZMQSchedulerCommunicator(s.requester, s.systemConfig, s.agvController, s, s.logger)
	s.logger.Info("ZMQ scheduler communicator created")

	return nil
}

// startHeartbeat 启动心跳管理器（带连接状态监控）
func (s *AutoWatchService) startHeartbeat() error {
	s.logger.Info("Starting heartbeat manager")

	// 创建心跳管理器
	s.heartbeatMgr = zmq.NewHeartbeatManagerWithLogger(s.requester, s.responder, s.systemConfig, s.logger)

	// 注册心跳管理器到消息路由器
	s.messageRouter.RegisterHandler(s.heartbeatMgr)

	// 在后台启动心跳和连接监控
	go s.heartbeatRoutine()

	s.logger.Info("Heartbeat manager started")
	return nil
}

// heartbeatRoutine 心跳协程（带重连机制）
func (s *AutoWatchService) heartbeatRoutine() {
	s.logger.Info("Heartbeat routine started")

	for {
		select {
		case <-s.ctx.Done():
			s.logger.Info("Heartbeat routine stopped by context")
			return
		default:
			// 尝试启动心跳管理器
			if err := s.heartbeatMgr.Start(s.ctx); err != nil {
				s.logger.Error("Heartbeat manager start failed", "error", err)
				s.mu.Lock()
				s.isConnected = false
				s.connectionStatus = "error"
				s.mu.Unlock()

				// 心跳失败，尝试重连
				if s.ctx.Err() == nil { // 确保服务未停止
					s.logger.Info("Attempting to reconnect ZMQ connections")
					if err := s.reconnectZMQ(); err != nil {
						s.logger.Error("Reconnect ZMQ failed", "error", err)
						// 等待后重试
						select {
						case <-s.ctx.Done():
							return
						case <-time.After(5 * time.Second):
							continue
						}
					}
				}
			} else {
				s.mu.Lock()
				s.isConnected = true
				s.connectionStatus = "connected"
				s.mu.Unlock()
				s.logger.Info("Heartbeat established successfully")

				// 心跳成功建立，启动连接状态监控
				s.monitorConnection()

				// 如果心跳管理器退出（可能是连接断开），重新循环
				s.logger.Warn("Heartbeat manager exited, will attempt reconnection")
				s.mu.Lock()
				s.isConnected = false
				s.connectionStatus = "disconnected"
				s.mu.Unlock()

				// 等待一段时间后重试
				select {
				case <-s.ctx.Done():
					return
				case <-time.After(3 * time.Second):
					continue
				}
			}
		}
	}
}

// monitorConnection 监控连接状态
func (s *AutoWatchService) monitorConnection() {
	s.logger.Info("Starting connection monitoring")

	ticker := time.NewTicker(10 * time.Second) // 每10秒检查一次连接状态
	defer ticker.Stop()

	for {
		select {
		case <-s.ctx.Done():
			s.logger.Info("Connection monitoring stopped by context")
			return
		case <-ticker.C:
			// 检查心跳状态
			if s.heartbeatMgr != nil {
				lastHeartbeat := s.heartbeatMgr.GetLastHeartbeat()
				if !lastHeartbeat.IsZero() {
					timeSinceLastHeartbeat := time.Since(lastHeartbeat)

					// 如果超过30秒没有心跳，认为连接有问题
					if timeSinceLastHeartbeat > 30*time.Second {
						s.logger.Warn("Heartbeat timeout detected", "timeSinceLastHeartbeat", timeSinceLastHeartbeat)
						s.mu.Lock()
						s.isConnected = false
						s.connectionStatus = "timeout"
						s.mu.Unlock()

						// 退出监控，让heartbeatRoutine处理重连
						return
					}

					// 连接正常
					s.mu.Lock()
					if s.connectionStatus != "connected" {
						s.connectionStatus = "connected"
						s.isConnected = true
						s.logger.Info("Connection status updated to connected")
					}
					s.mu.Unlock()
				}
			}
		}
	}
}

// reconnectZMQ 重连ZMQ连接
func (s *AutoWatchService) reconnectZMQ() error {
	s.logger.Info("Reconnecting ZMQ connections")

	// 清理现有连接
	s.cleanupZMQConnections()

	// 重新创建连接
	if err := s.createZMQConnections(); err != nil {
		return fmt.Errorf("recreate ZMQ connections failed: %w", err)
	}

	// 重新创建心跳管理器
	s.heartbeatMgr = zmq.NewHeartbeatManagerWithLogger(s.requester, s.responder, s.systemConfig, s.logger)

	// 重新注册心跳管理器到消息路由器
	s.messageRouter.RegisterHandler(s.heartbeatMgr)

	// 重新设置任务处理器
	if err := s.setupTaskHandler(); err != nil {
		return fmt.Errorf("re-setup task handler failed: %w", err)
	}

	s.logger.Info("ZMQ reconnection completed")
	return nil
}

// cleanupZMQConnections 清理ZMQ连接
func (s *AutoWatchService) cleanupZMQConnections() {
	s.logger.Info("Cleaning up ZMQ connections")

	if s.heartbeatMgr != nil {
		s.heartbeatMgr.Stop()
		s.heartbeatMgr = nil
	}

	if s.requester != nil {
		s.requester.Close()
		s.requester = nil
	}

	if s.responder != nil {
		s.responder.Close()
		s.responder = nil
	}

	s.logger.Info("ZMQ connections cleaned up")
}

// setupTaskHandler 设置任务消息处理器
func (s *AutoWatchService) setupTaskHandler() error {
	// 设置响应者使用消息路由器处理所有消息
	if s.responder != nil && s.messageRouter != nil {
		// 设置响应者的消息处理函数为路由器的RouteMessage方法
		if err := s.responder.SetHandler(s.messageRouter.RouteMessage); err != nil {
			return fmt.Errorf("set message router handler failed: %w", err)
		}
		s.logger.Info("Message router setup completed", "handlerCount", s.messageRouter.GetHandlerCount())
		s.logger.Info("Registered handlers", "handlers", s.messageRouter.GetHandlerNames())
	}
	return nil
}

// startResponder 启动响应者
func (s *AutoWatchService) startResponder() error {
	if s.responder == nil {
		return fmt.Errorf("responder not initialized")
	}

	// 在后台启动响应者
	go func() {
		if err := s.responder.Start(s.ctx); err != nil {
			s.logger.Error("Responder start failed", "error", err)
			s.mu.Lock()
			s.connectionStatus = "error"
			s.mu.Unlock()
		} else {
			s.logger.Info("Responder started successfully")
		}
	}()

	return nil
}

// cleanup 清理资源（复用cleanupZMQConnections）
func (s *AutoWatchService) cleanup() {
	s.cleanupZMQConnections()
}

// initializeComponents 初始化组件（复制自InitTaskManager逻辑）
func (s *AutoWatchService) initializeComponents() error {
	s.logger.Info("Initializing auto watch components")

	// 注意：数据库服务和其他业务组件的初始化已在 NewAutoWatchService() 中
	// 通过 configManager.InitializeComponents() 完成，此方法仅处理服务级别的配置

	// 从配置获取最大重试次数
	s.maxRetries = 3 // 暂时硬编码，后续从配置读取

	s.logger.Info("Auto watch components initialized")
	return nil
}

// checkAutoWatchRunCondition 检查自动看车运行条件（复制自checkRobotRunCondition）
func (s *AutoWatchService) checkAutoWatchRunCondition() error {
	s.logger.Info("Checking auto watch run condition")

	// 检查AGV控制器连接状态
	if s.agvController == nil {
		return fmt.Errorf("AGV控制器未初始化")
	}

	// 检查AGV连接状态
	if !s.agvController.IsConnected() {
		return fmt.Errorf("AGV未连接")
	}

	// 检查PLC控制器连接状态
	if s.plcController == nil {
		return fmt.Errorf("PLC控制器未初始化")
	}

	// 检查PLC连接状态
	if !s.plcController.IsConnected() {
		return fmt.Errorf("PLC未连接")
	}

	s.logger.Info("Auto watch run condition check passed")
	return nil
}

// startStatusReporter 启动状态上报器
func (s *AutoWatchService) startStatusReporter() error {
	if s.schedulerComm == nil {
		return fmt.Errorf("scheduler communicator not initialized")
	}

	s.logger.Info("Starting status reporter")

	// 启动状态上报器
	if err := s.schedulerComm.StartStatusReporter(s.ctx); err != nil {
		return fmt.Errorf("start status reporter failed: %w", err)
	}

	s.logger.Info("Status reporter started successfully")
	return nil
}

// initializeAutoWatchTaskState 初始化自动看车任务状态（复制自StartTask初始化部分）
func (s *AutoWatchService) initializeAutoWatchTaskState() {
	s.logger.Info("Initializing auto watch task state")

	// 重置任务状态
	s.queue = make([]string, 0)
	s.currentMachine = ""
	s.status = AutoWatchStatusIdle
	s.progress = 0
	s.total = 0
	s.isWorking = false

	// 重置错误处理状态
	s.failedMachines = make([]string, 0)
	s.lastError = ""

	// 重置进度跟踪状态
	s.taskStartTime = nil
	s.currentStep = "等待任务分配"
	s.stepProgress = 0
	s.completedTimes = make([]time.Time, 0)

	// 重置统计信息
	s.statistics.TotalTasks = 0
	s.statistics.CompletedTasks = 0
	s.statistics.FailedTasks = 0
	s.statistics.TotalRunTime = 0

	s.logger.Info("Auto watch task state initialized")
}

// stopAutoWatchTask 停止自动看车任务（复制自StopWatchTask逻辑）
func (s *AutoWatchService) stopAutoWatchTask() error {
	s.logger.Info("Stopping auto watch task")

	if !s.isWorking {
		return fmt.Errorf("没有正在运行的自动看车任务")
	}

	// 发送停止信号
	s.status = AutoWatchStatusStopped
	s.currentStep = "正在停止任务"

	// 发送停止信号到任务执行器
	select {
	case s.stopChan <- struct{}{}:
		s.logger.Info("Stop signal sent to task executor")
	default:
		s.logger.Warn("Stop channel is full, signal may be lost")
	}

	// 等待任务停止（带超时）
	timeout := time.After(30 * time.Second)
	ticker := time.NewTicker(100 * time.Millisecond)
	defer ticker.Stop()

	for {
		select {
		case <-timeout:
			s.logger.Error("Stop auto watch task timeout")
			return fmt.Errorf("停止自动看车任务超时")
		case <-ticker.C:
			if !s.isWorking {
				s.logger.Info("Auto watch task stopped successfully")
				return nil
			}
		}
	}
}

// resetAutoWatchTaskState 重置自动看车任务状态（复制自StopWatchTask的重置逻辑）
func (s *AutoWatchService) resetAutoWatchTaskState() {
	s.logger.Info("Resetting auto watch task state")

	// 重置任务执行状态
	s.queue = nil
	s.currentMachine = ""
	s.status = AutoWatchStatusIdle
	s.progress = 0
	s.total = 0
	s.isWorking = false

	// 重置任务控制通道（需要重新创建通道避免阻塞）
	s.stopChan = make(chan struct{})
	s.pauseChan = make(chan struct{})
	s.resumeChan = make(chan struct{})

	// 重置进度跟踪
	s.taskStartTime = nil
	s.currentStep = "服务已停止"
	s.stepProgress = 0

	// 保留统计信息和失败机器信息，便于查看历史情况
	// s.statistics - 保留
	// s.failedMachines - 保留
	// s.lastError - 保留
	// s.completedTimes - 保留

	s.logger.Info("Auto watch task state reset completed")
}

// calculateProcessingSpeed 计算处理速度（台/分钟）
func (s *AutoWatchService) calculateProcessingSpeed() float64 {
	if len(s.completedTimes) < 2 {
		return 0.0
	}

	// 使用最近的完成时间计算速度
	recentCount := len(s.completedTimes)
	if recentCount > 10 {
		recentCount = 10 // 只使用最近10台的数据
	}

	startIdx := len(s.completedTimes) - recentCount
	firstTime := s.completedTimes[startIdx]
	lastTime := s.completedTimes[len(s.completedTimes)-1]

	duration := lastTime.Sub(firstTime)
	if duration > 0 {
		// 速度 = 台数 / 分钟
		machinesProcessed := float64(recentCount - 1)
		minutes := duration.Minutes()
		return machinesProcessed / minutes
	}

	return 0.0
}

// calculateEstimatedTime 计算预计完成时间
func (s *AutoWatchService) calculateEstimatedTime(processingSpeed float64) *time.Time {
	if processingSpeed <= 0 || s.total <= s.progress {
		return nil
	}

	remaining := s.total - s.progress
	estimatedMinutes := float64(remaining) / processingSpeed

	estimatedTime := time.Now().Add(time.Duration(estimatedMinutes) * time.Minute)
	return &estimatedTime
}

// UpdateAutoWatchTaskProgress 更新自动看车任务进度（提供给任务执行器调用）
func (s *AutoWatchService) UpdateAutoWatchTaskProgress(currentMachine string, step string, stepProgress int) {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.currentMachine = currentMachine
	s.currentStep = step
	s.stepProgress = stepProgress

	s.logger.Debug("Auto watch task progress updated", "machine", currentMachine, "step", step, "progress", stepProgress)
}

// CompleteAutoWatchMachine 标记机器处理完成（提供给任务执行器调用）
func (s *AutoWatchService) CompleteAutoWatchMachine(machine string) {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.progress++
	s.completedTimes = append(s.completedTimes, time.Now())
	s.statistics.CompletedTasks++

	s.logger.Info("Auto watch machine completed", "machine", machine, "progress", s.progress, "total", s.total)
}

// FailAutoWatchMachine 标记机器处理失败（提供给任务执行器调用）
func (s *AutoWatchService) FailAutoWatchMachine(machine string, errorMsg string) {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.failedMachines = append(s.failedMachines, machine)
	s.lastError = errorMsg
	s.statistics.FailedTasks++

	s.logger.Error("Auto watch machine failed", "machine", machine, "error", errorMsg)
}

// SetAutoWatchTaskError 设置任务错误信息（提供给任务执行器调用）
func (s *AutoWatchService) SetAutoWatchTaskError(errorMsg string) {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.lastError = errorMsg
	s.currentStep = "任务异常"

	s.logger.Error("Auto watch task error", "error", errorMsg)
}

// GetAutoWatchTaskStatistics 获取任务统计信息（只读接口）
func (s *AutoWatchService) GetAutoWatchTaskStatistics() *Stats {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// 返回统计信息的副本
	return &Stats{
		TotalTasks:     s.statistics.TotalTasks,
		CompletedTasks: s.statistics.CompletedTasks,
		FailedTasks:    s.statistics.FailedTasks,
		TotalRunTime:   s.statistics.TotalRunTime,
	}
}

// GetAutoWatchConnectionInfo 获取连接信息（只读接口）
func (s *AutoWatchService) GetAutoWatchConnectionInfo() map[string]interface{} {
	s.mu.RLock()
	defer s.mu.RUnlock()

	info := map[string]interface{}{
		"isConnected":       s.isConnected,
		"connectionStatus":  s.connectionStatus,
		"robotNo":           s.systemConfig.RobotNo,
		"requesterEndpoint": s.zmqConfig.RequesterEndpoint,
		"responderEndpoint": s.zmqConfig.ResponderEndpoint,
	}

	if s.heartbeatMgr != nil {
		lastHeartbeat := s.heartbeatMgr.GetLastHeartbeat()
		if !lastHeartbeat.IsZero() {
			info["lastHeartbeat"] = lastHeartbeat.Format("2006-01-02 15:04:05")
			info["timeSinceLastHeartbeat"] = time.Since(lastHeartbeat).String()
		}
	}

	return info
}

// SetReceivedTask 设置接收到的任务信息（仅用于状态显示）
func (s *AutoWatchService) SetReceivedTask(taskId string, laneNos []string) {
	s.mu.Lock()
	defer s.mu.Unlock()

	// 创建简单的任务信息用于状态显示
	s.currentTask = &Task{
		TaskId:    taskId,
		MachineNo: fmt.Sprintf("lanes: %v", laneNos),
		TaskType:  "task_received",
		Status:    "received",
		Progress:  0,
	}
	s.statistics.TotalTasks++
}

// GetCurrentTaskId 获取当前任务ID
func (s *AutoWatchService) GetCurrentTaskId() string {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if s.currentTask != nil {
		return s.currentTask.TaskId
	}
	return ""
}

// NotifySchedulerTaskStart 通知调度系统任务开始
func (s *AutoWatchService) NotifySchedulerTaskStart(remark string) error {
	taskId := s.GetCurrentTaskId()
	if taskId == "" {
		return fmt.Errorf("no current task found")
	}

	if s.schedulerComm == nil {
		return fmt.Errorf("scheduler communicator not initialized")
	}

	s.logger.Info("Notifying scheduler of task start", "taskId", taskId, "remark", remark)
	return s.schedulerComm.SendTaskStartWithRetry(taskId, remark)
}

// NotifySchedulerTaskComplete 通知调度系统任务完成
func (s *AutoWatchService) NotifySchedulerTaskComplete(remark string) error {
	taskId := s.GetCurrentTaskId()
	if taskId == "" {
		return fmt.Errorf("no current task found")
	}

	if s.schedulerComm == nil {
		return fmt.Errorf("scheduler communicator not initialized")
	}

	s.logger.Info("Notifying scheduler of task complete", "taskId", taskId, "remark", remark)
	return s.schedulerComm.SendTaskCompleteWithRetry(taskId, remark)
}

// AGV位置状态管理方法

// UpdateAGVLocation 更新AGV位置状态
func (s *AutoWatchService) UpdateAGVLocation(location AGVLocation) {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.agvLocation != location {
		s.logger.Info("AGV location updated",
			"from", s.locationToString(s.agvLocation),
			"to", s.locationToString(location))
		s.agvLocation = location
	}
}

// GetAGVLocation 获取当前AGV位置状态
func (s *AutoWatchService) GetAGVLocation() AGVLocation {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.agvLocation
}

// locationToString 位置状态转字符串（用于日志）
func (s *AutoWatchService) locationToString(location AGVLocation) string {
	switch location {
	case LocationParkingPoint:
		return "parking_point"
	case LocationSwitchPoint:
		return "switch_point"
	case LocationUnknown:
		return "unknown"
	default:
		return "invalid"
	}
}

// HandleSchedulerCommand 处理调度系统指令
func (s *AutoWatchService) HandleSchedulerCommand(instruction int, content interface{}) error {
	s.logger.Info("Handling scheduler command", "instruction", instruction, "currentLocation", s.locationToString(s.GetAGVLocation()))

	switch instruction {
	case 200: // InstructionTaskAssignment - 任务下发
		return s.handleNewTaskAssignment(content)
	case 201: // InstructionNoTask - 无任务，返回停车点
		return s.handleReturnToParking()
	case 202: // InstructionWaitTask - 等待任务，原地待命
		return s.handleWaitInPlace()
	case 203: // InstructionContinueTask - 继续任务（支持新任务分配）
		return s.handleContinueTask(content)
	default:
		return fmt.Errorf("unsupported scheduler command: %d", instruction)
	}
}

// handleNewTaskAssignment 处理新任务分配（指令200）
func (s *AutoWatchService) handleNewTaskAssignment(content interface{}) error {
	s.logger.Info("Processing new task assignment command")

	// 解析任务内容（这里复用TaskMessageHandler的逻辑）
	contentMap, ok := content.(map[string]interface{})
	if !ok {
		return fmt.Errorf("invalid task assignment content format")
	}

	taskId, ok := contentMap["taskId"].(string)
	if !ok {
		return fmt.Errorf("missing or invalid taskId in task assignment")
	}

	robotNo, ok := contentMap["robotNo"].(string)
	if !ok {
		return fmt.Errorf("missing or invalid robotNo in task assignment")
	}

	// 验证机器人编号
	if robotNo != s.systemConfig.RobotNo {
		return fmt.Errorf("task assignment for different robot: %s, local: %s", robotNo, s.systemConfig.RobotNo)
	}

	// 解析LaneNos数组
	var laneNos []string
	if laneNosInterface, exists := contentMap["laneNos"]; exists {
		if laneNosArray, ok := laneNosInterface.([]interface{}); ok {
			for _, lane := range laneNosArray {
				if laneStr, ok := lane.(string); ok {
					laneNos = append(laneNos, laneStr)
				}
			}
		}
	}

	if len(laneNos) == 0 {
		return fmt.Errorf("no machines specified in task assignment")
	}

	// 解析remark字段（可选）
	remark, _ := contentMap["remark"].(string)

	s.logger.Info("New task assignment accepted",
		"taskId", taskId,
		"robotNo", robotNo,
		"laneCount", len(laneNos),
		"lanes", laneNos,
		"remark", remark)

	// 保存新任务信息
	s.SetReceivedTask(taskId, laneNos)

	// 启动新任务执行
	if s.taskExecutor != nil {
		s.logger.Info("Starting new task execution",
			"taskId", taskId,
			"machines", laneNos,
			"remark", remark)

		// 在新的goroutine中执行任务，避免阻塞
		go func() {
			err := s.taskExecutor.AutoExecuteTask(laneNos)
			if err != nil {
				s.logger.Error("New task execution failed",
					"taskId", taskId,
					"remark", remark,
					"error", err)
			} else {
				s.logger.Info("New task completed successfully",
					"taskId", taskId,
					"remark", remark)
			}
		}()
	} else {
		return fmt.Errorf("task executor not available")
	}

	return nil
}

// handleReturnToParking 处理返回停车点（指令201）
func (s *AutoWatchService) handleReturnToParking() error {
	s.logger.Info("Processing return to parking command", "currentLocation", s.locationToString(s.GetAGVLocation()))

	currentLocation := s.GetAGVLocation()

	switch currentLocation {
	case LocationParkingPoint:
		s.logger.Info("AGV already at parking point, no action needed")
		return nil

	case LocationSwitchPoint, LocationUnknown:
		s.logger.Info("AGV not at parking point, starting navigation to parking")

		// 使用现有的导航到停车点方法
		if s.navigationHandler != nil {
			err := s.navigationHandler.AutoNavigateToParking()
			if err != nil {
				s.logger.Error("Navigate to parking failed", "error", err)
				return fmt.Errorf("navigate to parking failed: %w", err)
			}

			// 更新位置状态
			s.UpdateAGVLocation(LocationParkingPoint)
			s.logger.Info("Successfully returned to parking point")
			return nil
		} else {
			return fmt.Errorf("navigation handler not available")
		}

	default:
		return fmt.Errorf("unknown AGV location state: %d", currentLocation)
	}
}

// handleWaitInPlace 处理原地等待（指令202）
func (s *AutoWatchService) handleWaitInPlace() error {
	s.logger.Info("Processing wait in place command", "currentLocation", s.locationToString(s.GetAGVLocation()))

	// 设置状态为等待
	s.mu.Lock()
	s.currentStep = "等待调度系统指令"
	s.status = AutoWatchStatusIdle
	s.mu.Unlock()

	s.logger.Info("AGV now waiting for further instructions")
	return nil
}

// handleContinueTask 处理继续任务（指令203）- 支持新任务分配和恢复暂停任务
func (s *AutoWatchService) handleContinueTask(content interface{}) error {
	s.logger.Info("Processing continue task command")

	// 检查是否包含新的任务数据
	if content != nil {
		if hasNewTaskData := s.checkForNewTaskData(content); hasNewTaskData {
			s.logger.Info("Continue task command contains new task assignment data")
			return s.handleNewTaskInContinueCommand(content)
		}
	}

	// 如果没有新任务数据，则处理恢复已有任务的逻辑
	return s.handleResumeExistingTask()
}

// checkForNewTaskData 检查content中是否包含新任务数据
func (s *AutoWatchService) checkForNewTaskData(content interface{}) bool {
	contentMap, ok := content.(map[string]interface{})
	if !ok {
		return false
	}

	// 检查是否包含任务相关字段
	if _, hasTaskId := contentMap["taskId"]; hasTaskId {
		return true
	}
	if _, hasLaneNos := contentMap["laneNos"]; hasLaneNos {
		return true
	}
	if _, hasRobotNo := contentMap["robotNo"]; hasRobotNo {
		return true
	}

	return false
}

// handleNewTaskInContinueCommand 处理指令203中的新任务分配
func (s *AutoWatchService) handleNewTaskInContinueCommand(content interface{}) error {
	s.logger.Info("Processing new task assignment in continue command")

	// 解析任务内容（复用现有逻辑）
	contentMap, ok := content.(map[string]interface{})
	if !ok {
		return fmt.Errorf("invalid task assignment content format in continue command")
	}

	taskId, ok := contentMap["taskId"].(string)
	if !ok {
		return fmt.Errorf("missing or invalid taskId in continue command")
	}

	robotNo, ok := contentMap["robotNo"].(string)
	if !ok {
		return fmt.Errorf("missing or invalid robotNo in continue command")
	}

	// 验证机器人编号
	if robotNo != s.systemConfig.RobotNo {
		return fmt.Errorf("continue command task for different robot: %s, local: %s", robotNo, s.systemConfig.RobotNo)
	}

	// 解析LaneNos数组
	var laneNos []string
	if laneNosInterface, exists := contentMap["laneNos"]; exists {
		if laneNosArray, ok := laneNosInterface.([]interface{}); ok {
			for _, lane := range laneNosArray {
				if laneStr, ok := lane.(string); ok {
					laneNos = append(laneNos, laneStr)
				}
			}
		}
	}

	if len(laneNos) == 0 {
		return fmt.Errorf("no machines specified in continue command")
	}

	// 解析remark字段（可选）
	remark, _ := contentMap["remark"].(string)

	s.logger.Info("Continue command with new task accepted",
		"taskId", taskId,
		"robotNo", robotNo,
		"laneCount", len(laneNos),
		"lanes", laneNos,
		"remark", remark,
		"currentLocation", s.locationToString(s.GetAGVLocation()))

	// 保存新任务信息
	s.SetReceivedTask(taskId, laneNos)

	// 启动新任务执行
	if s.taskExecutor != nil {
		s.logger.Info("Starting new task execution from continue command",
			"taskId", taskId,
			"machines", laneNos,
			"remark", remark)

		// 在新的goroutine中执行任务，避免阻塞
		go func() {
			err := s.taskExecutor.AutoExecuteTask(laneNos)
			if err != nil {
				s.logger.Error("Continue command new task execution failed",
					"taskId", taskId,
					"remark", remark,
					"error", err)
			} else {
				s.logger.Info("Continue command new task completed successfully",
					"taskId", taskId,
					"remark", remark)
			}
		}()
	} else {
		return fmt.Errorf("task executor not available for continue command")
	}

	return nil
}

// handleResumeExistingTask 处理恢复已有任务的逻辑
func (s *AutoWatchService) handleResumeExistingTask() error {
	s.logger.Info("Processing resume existing task")

	// 检查是否有当前任务
	currentTaskId := s.GetCurrentTaskId()
	if currentTaskId == "" {
		s.logger.Warn("No current task to resume, continue command ignored")
		return nil // 不报错，只是没有任务可恢复
	}

	// 检查任务执行器状态
	if s.taskExecutor == nil {
		return fmt.Errorf("task executor not available")
	}

	// 如果任务是暂停状态，则恢复
	if s.status == AutoWatchStatusPaused {
		s.logger.Info("Resuming paused task", "taskId", currentTaskId)
		s.taskExecutor.Resume()
		s.mu.Lock()
		s.status = AutoWatchStatusRunning
		s.currentStep = "恢复任务执行"
		s.mu.Unlock()
	} else {
		s.logger.Info("Task continue command received but task not paused",
			"status", s.status,
			"taskId", currentTaskId)
	}

	return nil
}

// 配置管理相关方法

// GetConfig 获取当前配置（只读）
func (s *AutoWatchService) GetConfig() *AutoWatchConfig {
	if s.configManager == nil {
		return nil
	}
	return s.configManager.GetConfig()
}

// UpdateConfig 更新配置
func (s *AutoWatchService) UpdateConfig(config *AutoWatchConfig) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.configManager == nil {
		return fmt.Errorf("config manager not initialized")
	}

	// 验证配置
	if err := s.configManager.ValidateConfig(config); err != nil {
		return fmt.Errorf("config validation failed: %w", err)
	}

	// 检查是否需要重启组件
	oldConfig := s.configManager.GetConfig()
	needsRestart := s.configRequiresRestart(oldConfig, config)

	// 更新配置
	if err := s.configManager.UpdateConfig(config); err != nil {
		return fmt.Errorf("config update failed: %w", err)
	}

	// 如果需要重启组件，重新初始化
	if needsRestart {
		s.logger.Info("Configuration changed requires component restart")
		if err := s.reinitializeComponents(); err != nil {
			s.logger.Error("Component reinitialization failed", "error", err)
			return fmt.Errorf("component reinitialization failed: %w", err)
		}
	}

	s.logger.Info("Configuration updated successfully", "needsRestart", needsRestart)
	return nil
}

// GetSystemStatus 获取系统状态
func (s *AutoWatchService) GetSystemStatus() map[string]interface{} {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if s.configManager == nil {
		return map[string]interface{}{
			"error": "config manager not initialized",
		}
	}

	// 创建组件集合
	components := &AutoWatchComponentSet{
		AGVController:     s.agvController,
		PLCController:     s.plcController,
		DBService:         s.dbService,
		Config:            s.configManager.GetConfig(),
		Logger:            s.logger,
		SpindleService:    s.spindleService,
		NavigationHandler: s.navigationHandler,
		PLCHandler:        s.plcHandler,
		WorkExecutor:      s.workExecutor,
		LaneProcessor:     s.laneProcessor,
		TaskExecutor:      s.taskExecutor,
	}

	// 获取系统状态
	status := s.configManager.GetSystemStatus(components)

	// 添加服务特定状态
	status["service"] = map[string]interface{}{
		"isRunning":        s.isRunning,
		"isConnected":      s.isConnected,
		"connectionStatus": s.connectionStatus,
		"taskStatus":       s.status,
		"currentMachine":   s.currentMachine,
		"progress":         s.progress,
		"total":            s.total,
		"isWorking":        s.isWorking,
	}

	return status
}

// ValidateCurrentConfig 验证当前配置
func (s *AutoWatchService) ValidateCurrentConfig() error {
	if s.configManager == nil {
		return fmt.Errorf("config manager not initialized")
	}

	config := s.configManager.GetConfig()
	if config == nil {
		return fmt.Errorf("no config found")
	}

	return s.configManager.ValidateConfig(config)
}

// ReloadConfig 重新加载配置（从文件或默认值）
func (s *AutoWatchService) ReloadConfig() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.configManager == nil {
		return fmt.Errorf("config manager not initialized")
	}

	// 创建新的默认配置
	newConfig := DefaultAutoWatchConfig()

	// 验证配置
	if err := s.configManager.ValidateConfig(newConfig); err != nil {
		return fmt.Errorf("default config validation failed: %w", err)
	}

	// 更新配置
	if err := s.configManager.UpdateConfig(newConfig); err != nil {
		return fmt.Errorf("config reload failed: %w", err)
	}

	// 重新初始化组件
	if err := s.reinitializeComponents(); err != nil {
		return fmt.Errorf("component reinitialization failed: %w", err)
	}

	s.logger.Info("Configuration reloaded successfully")
	return nil
}

// configRequiresRestart 检查配置变更是否需要重启组件
func (s *AutoWatchService) configRequiresRestart(oldConfig, newConfig *AutoWatchConfig) bool {
	if oldConfig == nil || newConfig == nil {
		return true
	}

	// 检查关键配置项是否发生变化
	if oldConfig.PLC.PLCAddresses != newConfig.PLC.PLCAddresses {
		return true
	}

	if oldConfig.Navigation.NavigationTimeout != newConfig.Navigation.NavigationTimeout {
		return true
	}

	if oldConfig.Spindle.MESEndpoint != newConfig.Spindle.MESEndpoint {
		return true
	}

	if oldConfig.Task.MaxRetries != newConfig.Task.MaxRetries {
		return true
	}

	// 其他配置项变更通常不需要重启
	return false
}

// === 增强的错误处理结构 ===

// AutoWatchConfigUpdateError 配置更新错误
type AutoWatchConfigUpdateError struct {
	Operation string                           // 操作类型：validation, update, restart_check, component_restart
	Reason    string                           // 错误原因
	Report    *AutoWatchConfigValidationReport // 验证报告（如果有）
	Cause     error                            // 根本错误原因
}

func (e *AutoWatchConfigUpdateError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("config %s failed: %s (cause: %v)", e.Operation, e.Reason, e.Cause)
	}
	return fmt.Sprintf("config %s failed: %s", e.Operation, e.Reason)
}

func (e *AutoWatchConfigUpdateError) Unwrap() error {
	return e.Cause
}

// SaveConfigToFile 保存配置到文件（带错误处理）
func (s *AutoWatchService) SaveConfigToFile(filepath string) error {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if s.configManager == nil {
		return fmt.Errorf("config manager not initialized")
	}

	return s.configManager.SaveConfigToFile(filepath)
}

// LoadConfigFromFile 从文件加载配置（带错误处理）
func (s *AutoWatchService) LoadConfigFromFile(filepath string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.configManager == nil {
		return fmt.Errorf("config manager not initialized")
	}

	// 运行时检查
	if s.isRunning {
		return &AutoWatchConfigUpdateError{
			Operation: "load_file",
			Reason:    "cannot load config from file while task is running",
			Cause:     fmt.Errorf("service is currently running tasks"),
		}
	}

	// 从文件加载配置
	if err := s.configManager.LoadConfigFromFile(filepath); err != nil {
		return &AutoWatchConfigUpdateError{
			Operation: "load_file",
			Reason:    "failed to load configuration from file",
			Cause:     err,
		}
	}

	// 重新初始化组件
	if err := s.reinitializeComponents(); err != nil {
		return &AutoWatchConfigUpdateError{
			Operation: "component_restart",
			Reason:    "component reinitialization failed after loading config from file",
			Cause:     err,
		}
	}

	s.logger.Info("Auto watch config loaded from file successfully", "filepath", filepath)
	return nil
}

// reinitializeComponents 重新初始化组件
func (s *AutoWatchService) reinitializeComponents() error {
	if s.isWorking {
		return fmt.Errorf("cannot reinitialize components while task is running")
	}

	s.logger.Info("Reinitializing auto watch components")

	// 重新初始化所有业务组件
	components, err := s.configManager.InitializeComponents(s.agvController, s.plcController)
	if err != nil {
		return fmt.Errorf("component reinitialization failed: %w", err)
	}

	// 更新组件引用
	s.taskExecutor = components.TaskExecutor
	s.spindleService = components.SpindleService
	s.navigationHandler = components.NavigationHandler
	s.plcHandler = components.PLCHandler
	s.laneProcessor = components.LaneProcessor
	s.workExecutor = components.WorkExecutor

	// 为工作执行器设置服务实例（用于任务完成通知）
	if s.workExecutor != nil {
		s.workExecutor.SetService(s)
	}

	// 为巷道处理器设置服务实例（用于任务完成通知）
	if s.laneProcessor != nil {
		s.laneProcessor.SetService(s)
	}
	s.dbService = components.DBService

	s.logger.Info("Auto watch components reinitialized successfully")
	return nil
}

// GetComponentsHealth 获取组件健康状态
func (s *AutoWatchService) GetComponentsHealth() map[string]interface{} {
	s.mu.RLock()
	defer s.mu.RUnlock()

	health := map[string]interface{}{
		"timestamp": time.Now(),
	}

	// 检查各个组件的健康状态
	health["agvController"] = map[string]interface{}{
		"available": s.agvController != nil,
		"connected": s.agvController != nil && s.agvController.IsConnected(),
	}

	health["plcController"] = map[string]interface{}{
		"available": s.plcController != nil,
		"connected": s.plcController != nil && s.plcController.IsConnected(),
	}

	health["dbService"] = map[string]interface{}{
		"available": s.dbService != nil,
	}

	health["taskExecutor"] = map[string]interface{}{
		"available": s.taskExecutor != nil,
		"running":   s.taskExecutor != nil && s.taskExecutor.IsRunning(),
	}

	health["spindleService"] = map[string]interface{}{
		"available": s.spindleService != nil,
	}

	health["navigationHandler"] = map[string]interface{}{
		"available": s.navigationHandler != nil,
	}

	health["plcHandler"] = map[string]interface{}{
		"available": s.plcHandler != nil,
	}

	health["workExecutor"] = map[string]interface{}{
		"available": s.workExecutor != nil,
	}

	health["laneProcessor"] = map[string]interface{}{
		"available": s.laneProcessor != nil,
	}

	health["configManager"] = map[string]interface{}{
		"available": s.configManager != nil,
	}

	return health
}

// PauseTask 暂停自动看车任务（全局接口，复制自task_service.go）
func PauseTask() error {
	if globalAutoWatchService == nil {
		return fmt.Errorf("auto watch service not initialized")
	}

	globalAutoWatchService.mu.Lock()
	defer globalAutoWatchService.mu.Unlock()

	if !globalAutoWatchService.isWorking || globalAutoWatchService.status != AutoWatchStatusRunning {
		return fmt.Errorf("没有可暂停的任务")
	}

	globalAutoWatchService.logger.Info("发送暂停信号")

	// 发送暂停信号到service级别的通道（非阻塞）
	select {
	case globalAutoWatchService.pauseChan <- struct{}{}:
	default:
	}

	// 同时暂停任务执行器
	if globalAutoWatchService.taskExecutor != nil {
		globalAutoWatchService.taskExecutor.Pause()
	}

	// 更新状态
	globalAutoWatchService.status = AutoWatchStatusPaused

	// 在释放锁之后再广播任务状态变化（避免死锁）
	go func() {
		time.Sleep(10 * time.Millisecond)
		// 这里需要广播状态，但为了避免循环依赖，先记录日志
		globalAutoWatchService.logger.Info("Auto watch task paused, status broadcasted")
	}()

	return nil
}

// ResumeTask 恢复自动看车任务（全局接口，复制自task_service.go）
func ResumeTask() error {
	if globalAutoWatchService == nil {
		return fmt.Errorf("auto watch service not initialized")
	}

	globalAutoWatchService.mu.Lock()
	defer globalAutoWatchService.mu.Unlock()

	if !globalAutoWatchService.isWorking || globalAutoWatchService.status != AutoWatchStatusPaused {
		return fmt.Errorf("没有可恢复的任务")
	}

	globalAutoWatchService.logger.Info("发送恢复信号")

	// 发送恢复信号到service级别的通道（非阻塞）
	select {
	case globalAutoWatchService.resumeChan <- struct{}{}:
	default:
	}

	// 同时恢复任务执行器
	if globalAutoWatchService.taskExecutor != nil {
		globalAutoWatchService.taskExecutor.Resume()
	}

	// 更新状态
	globalAutoWatchService.status = AutoWatchStatusRunning

	// 在释放锁之后再广播任务状态变化（避免死锁）
	go func() {
		time.Sleep(10 * time.Millisecond)
		// 这里需要广播状态，但为了避免循环依赖，先记录日志
		globalAutoWatchService.logger.Info("Auto watch task resumed, status broadcasted")
	}()

	return nil
}

// GetAutoWatchTaskStatus 获取自动看车任务状态（全局接口，对应单机看车的GetTaskStatus）
func GetAutoWatchTaskStatus() map[string]interface{} {
	if globalAutoWatchService == nil {
		return map[string]interface{}{
			"error": "auto watch service not initialized",
		}
	}

	// 直接使用service的GetStatus方法，但转换为与单机看车兼容的格式
	status := globalAutoWatchService.GetStatus()

	// 转换为与单机看车GetTaskStatus相同的格式
	return map[string]interface{}{
		"status":          string(status.TaskStatus),
		"current_machine": status.CurrentMachine,
		"progress":        status.ProcessedCount,
		"total":           status.TotalCount,
		"remaining":       status.MachineList[status.ProcessedCount:], // 剩余机器列表
		"failed_machines": []string{},                                 // TODO: 需要从统计信息中获取失败机器列表
		"last_error":      status.ErrorMessage,
		"progress_percent": func() int {
			if status.TotalCount > 0 {
				return (status.ProcessedCount * 100) / status.TotalCount
			}
			return 0
		}(),
		"current_step":     status.ProgressDetail.CurrentStep,
		"step_progress":    status.ProgressDetail.StepProgress,
		"processing_speed": 0.0, // TODO: 从统计信息计算处理速度
		"estimated_time":   nil, // TODO: 从统计信息计算预估时间
		"start_time":       status.StartTime,
		"elapsed_time": func() string {
			if status.StartTime != nil {
				elapsed := time.Since(*status.StartTime)
				return elapsed.String()
			}
			return ""
		}(),
	}
}
